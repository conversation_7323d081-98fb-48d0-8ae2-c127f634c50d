import React from 'react';
import { StatusBar } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import Toast from 'react-native-toast-message';
import { Provider } from 'react-redux';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import { ThemeProvider } from '@rneui/themed';

import AppNavigationContainer from '@/navigation/NavigationContainer';
import { store } from '@/store';
import { theme } from '@/theme';

import '@/localization/i18n';

GoogleSignin.configure({
  webClientId:
    '1063427509553-2gibh63lp56iclvq8be856sarcp3gj7v.apps.googleusercontent.com',
  iosClientId:
    '1063427509553-0su4g3pk8uk5ceat14075cdmnr0la8dj.apps.googleusercontent.com',
  //scopes: ['profile', 'email'],
});

export default function App(): React.JSX.Element {
  return (
    <ThemeProvider theme={theme}>
      <SafeAreaProvider>
        <Provider store={store}>
          <StatusBar />
          <AppNavigationContainer />
          <Toast />
        </Provider>
      </SafeAreaProvider>
    </ThemeProvider>
  );
}
