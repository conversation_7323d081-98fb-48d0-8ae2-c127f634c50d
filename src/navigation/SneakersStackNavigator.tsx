import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';

import SingleSneaker from '@/screens/single-sneaker';
import Sneakers from '@/screens/sneakers';
import { APP_ROUTES, SneakersStackNavigatorParamList } from '@/types/routes';

const Stack = createStackNavigator<SneakersStackNavigatorParamList>();

const SneakersStackNavigator = () => (
  <Stack.Navigator screenOptions={{ headerShown: false }}>
    <Stack.Screen name="SneakersList" component={Sneakers} />
    <Stack.Screen name={APP_ROUTES.SINGLE_SNEAKER} component={SingleSneaker} />
  </Stack.Navigator>
);

export default SneakersStackNavigator;
