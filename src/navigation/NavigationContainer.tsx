import React, { useEffect } from 'react';
import { DarkTheme, NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

import { useAppDispatch, useAppSelector } from '@/hooks/redux';
import { fetchUserDetails } from '@/store/slices/authSlice';
import { RootStackParamList, STACKS } from '@/types/routes';

import AppNavigator from './AppNavigator';
import AuthNavigator from './AuthNavigator';
import { navigationRef } from './NavigationRef';

const RootStack = createNativeStackNavigator<RootStackParamList>();

// 1. Create a custom theme based on the DarkTheme
const CustomTheme = {
  ...DarkTheme,
  colors: {
    ...DarkTheme.colors,
    background: '#030712', // Set the main background color
    card: '#030712', // Set the background color for stack screens
  },
};

const AppNavigationContainer = () => {
  const { access_token } = useAppSelector(state => state.auth);
  const dispatch = useAppDispatch();

  useEffect(() => {
    // Only fetch user details if there's a token
    if (access_token) {
      dispatch(fetchUserDetails());
    }
  }, [access_token, dispatch]); // Added dependencies to useEffect

  return (
    // 2. Apply the custom theme to the NavigationContainer
    <NavigationContainer ref={navigationRef} theme={CustomTheme}>
      <RootStack.Navigator>
        {access_token ? (
          <RootStack.Screen
            name={STACKS.APP}
            component={AppNavigator}
            options={{ headerShown: false }}
          />
        ) : (
          <RootStack.Screen
            name={STACKS.AUTH}
            component={AuthNavigator}
            options={{ headerShown: false }}
          />
        )}
      </RootStack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigationContainer;
