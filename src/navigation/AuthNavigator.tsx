import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';

import AppHeader from '@/components/AppHeader';
import ForgotPasswordScreen from '@/screens/forgot-passoword';
import OnboardingScreen from '@/screens/onboarding/OnboardingScreen';
import ResetPasswordScreen from '@/screens/reset-passoword';
import EmailSignInScreen from '@/screens/signin/EmailSignInScreen';
import SocialSignInScreen from '@/screens/signin/SocialSignInScreen';
import SignupScreen from '@/screens/signup';
import CheckEmail from '@/screens/signup/CheckEmail';
import LanguageRegionScreen from '@/screens/signup/LanguageRegionScreen';
import { AUTH_ROUTES, AuthStackNavigatorParamList } from '@/types/routes';

const Stack = createStackNavigator<AuthStackNavigatorParamList>();

const AuthNavigator = () => {
  return (
    <Stack.Navigator
      initialRouteName={AUTH_ROUTES.ONBOARDING}
      screenOptions={{
        header: AppHeader,
      }}
    >
      <Stack.Screen
        name={AUTH_ROUTES.ONBOARDING}
        options={{
          title: 'Onboarding',
          headerShown: false,
        }}
        component={OnboardingScreen}
      />
      <Stack.Screen
        name={AUTH_ROUTES.SOCIAL_SIGNIN}
        component={SocialSignInScreen}
        options={{
          title: 'Social Sign In',
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={AUTH_ROUTES.EMAIL_SIGNIN}
        component={EmailSignInScreen}
      />
      <Stack.Screen
        name={AUTH_ROUTES.SIGNUP}
        options={{
          title: 'Register',
        }}
        component={SignupScreen}
      />
      <Stack.Screen
        name={AUTH_ROUTES.FORGOT_PASSWORD}
        options={{
          title: 'Forgot Password',
        }}
        component={ForgotPasswordScreen}
      />
      <Stack.Screen
        name={AUTH_ROUTES.RESET_PASSWORD}
        options={{
          title: 'Reset Password',
        }}
        component={ResetPasswordScreen}
      />
      <Stack.Screen
        name={AUTH_ROUTES.CHECK_EMAIL}
        options={{
          title: 'Register',
        }}
        component={CheckEmail}
      />
      <Stack.Screen
        name={AUTH_ROUTES.LANGUAGE_REGION}
        options={{
          title: 'Register',
        }}
        component={LanguageRegionScreen}
      />
    </Stack.Navigator>
  );
};

export default AuthNavigator;
