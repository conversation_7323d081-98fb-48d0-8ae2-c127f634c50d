import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';

import {
  CashbackIcon,
  DealsIcon,
  HomeIcon,
  ProfileIcon,
  SneakersIcon,
} from '@/constants/svgs'; // Make sure this path is correct
import CashbackScreen from '@/screens/cashback/index';
import Deals from '@/screens/deals';
import Home from '@/screens/home';
import ProfileScreen from '@/screens/profile';
import { APP_ROUTES, BottomTabNavigatorParamList } from '@/types/routes';

import SneakersStackNavigator from './SneakersStackNavigator';

const Tab = createBottomTabNavigator<BottomTabNavigatorParamList>();

const renderHomeIcon = ({ color, size }: { color: string; size: number }) => (
  <HomeIcon color={color} width={size} height={size} />
);
const renderDealsIcon = ({ color, size }: { color: string; size: number }) => (
  <DealsIcon color={color} width={size} height={size} />
);
const renderSneakersIcon = ({
  color,
  size,
}: {
  color: string;
  size: number;
}) => <SneakersIcon color={color} width={size} height={size} />;
const renderCashbackIcon = ({
  color,
  size,
}: {
  color: string;
  size: number;
}) => <CashbackIcon color={color} width={size} height={size} />;
const renderProfileIcon = ({
  color,
  size,
}: {
  color: string;
  size: number;
}) => <ProfileIcon color={color} width={size} height={size} />;

const BottomTabNavigator = () => {
  return (
    <Tab.Navigator
      initialRouteName={APP_ROUTES.HOME}
      screenOptions={{
        headerShown: false,
        // You can set active/inactive colors for all tabs here
        tabBarActiveTintColor: '#F5A623',
        tabBarInactiveTintColor: '#8E8E93',
      }}
    >
      <Tab.Screen
        name={APP_ROUTES.HOME}
        component={Home}
        options={{
          title: 'Home',
          tabBarIcon: renderHomeIcon,
        }}
      />
      <Tab.Screen
        name={APP_ROUTES.DEALS}
        component={Deals}
        options={{
          title: 'Deals',
          tabBarIcon: renderDealsIcon,
        }}
      />
      <Tab.Screen
        name={APP_ROUTES.SNEAKERS}
        component={SneakersStackNavigator}
        options={{
          title: 'Sneakers',
          tabBarIcon: renderSneakersIcon,
        }}
      />
      <Tab.Screen
        name={APP_ROUTES.CASHBACK}
        component={CashbackScreen}
        options={{
          title: 'Cashback',
          tabBarIcon: renderCashbackIcon,
        }}
      />
      <Tab.Screen
        name={APP_ROUTES.PROFILE}
        component={ProfileScreen}
        options={{
          title: 'Profile',
          tabBarIcon: renderProfileIcon,
        }}
      />
    </Tab.Navigator>
  );
};

export default BottomTabNavigator;
