import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';

import StoreDetailScreen from '@/screens/cashback/StoreDetailScreen';
import ChangePasswordScreen from '@/screens/change-password';
import DealDetailScreen from '@/screens/deals/DealDetailScreen';
import AllOffersScreen from '@/screens/offers';
import NotificationScreen from '@/screens/notification';
import PasswordSecurity from '@/screens/password-security';
import RewardsScreen from '@/screens/rewards';
import PayoutMethodsScreen from '@/screens/payout-methods';
import EditPayoutMethodScreen from '@/screens/payout-methods/EditPayoutMethod';
import PersonalInformation from '@/screens/personal-information';
import ProfileSettings from '@/screens/profile-settings';
import LanguageRegionScreen from '@/screens/signup/LanguageRegionScreen';
import SingleSneaker from '@/screens/single-sneaker';
import AllStoresScreen from '@/screens/stores';
import { APP_ROUTES, AppStackNavigatorParamList } from '@/types/routes';

import BottomTabNavigator from './BottomTabNavigator';

const Stack = createStackNavigator<AppStackNavigatorParamList>();

const AppNavigator = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen
        name={APP_ROUTES.DASHBOARD}
        component={BottomTabNavigator}
      />
      <Stack.Screen
        name={APP_ROUTES.SINGLE_SNEAKER}
        component={SingleSneaker}
      />
      <Stack.Screen
        name={APP_ROUTES.DEAL_DETAIL}
        component={DealDetailScreen}
      />
      <Stack.Screen
        name={APP_ROUTES.PROFILE_SETTINGS}
        component={ProfileSettings}
      />
      <Stack.Screen
        name={APP_ROUTES.PERSONAL_INFORMATION}
        component={PersonalInformation}
      />
      <Stack.Screen
        name={APP_ROUTES.PAYOUT_METHODS}
        component={PayoutMethodsScreen}
      />
      <Stack.Screen
        name={APP_ROUTES.EDIT_PAYOUT_METHOD}
        component={EditPayoutMethodScreen}
      />
      <Stack.Screen
        name={APP_ROUTES.PASSWORD_SECURITY}
        component={PasswordSecurity}
      />
      <Stack.Screen
        name={APP_ROUTES.CHANGE_PASSWORD}
        component={ChangePasswordScreen}
      />
      <Stack.Screen
        name={APP_ROUTES.LANGUAGE_REGION}
        component={LanguageRegionScreen}
      />
      <Stack.Screen
        name={APP_ROUTES.STORE_DETAIL}
        component={StoreDetailScreen}
      />
      <Stack.Screen name={APP_ROUTES.ALL_STORES} component={AllStoresScreen} />
      <Stack.Screen name={APP_ROUTES.ALL_OFFERS} component={AllOffersScreen} />
      <Stack.Screen name={APP_ROUTES.NOTIFICATION} component={NotificationScreen} />
      <Stack.Screen name={APP_ROUTES.REWARDS} component={RewardsScreen} />
    </Stack.Navigator>
  );
};

export default AppNavigator;
