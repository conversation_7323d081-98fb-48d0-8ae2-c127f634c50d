import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { DealsService } from '@/services/deals';
import { PromotedOffer } from '@/types/deals';

interface DealsState {
  deals: PromotedOffer[];
  productCategories: string[];
  loading: boolean;
  categoryLoading: boolean; // Separate loading state for category switches
  error: string | null;
}

const initialState: DealsState = {
  deals: [],
  productCategories: [],
  loading: false,
  categoryLoading: false,
  error: null,
};

export const fetchDeals = createAsyncThunk(
  'deals/fetchDeals',
  async (categoryId: string | undefined, thunkAPI) => {
    const { rejectWithValue, getState } = thunkAPI;
    const state = getState() as { deals: DealsState };
    const isInitialLoad = state.deals.deals.length === 0;

    try {
      const data = await DealsService.getDeals(categoryId);

      // data is the full API response
      return {
        deals: data.data,
        productCategories: data.product_categories,
        isInitialLoad,
      };
    } catch (error: unknown) {
      const err = error as Error;

      return rejectWithValue(err.message || 'Failed to fetch deals');
    }
  },
);

const dealsSlice = createSlice({
  name: 'deals',
  initialState,
  reducers: {
    clearCategoryLoading: state => {
      state.categoryLoading = false;
    },
  },
  extraReducers: builder => {
    builder
      .addCase(fetchDeals.pending, (state, _action) => {
        const isInitialLoad =
          state.deals.length === 0 && state.productCategories.length === 0;

        if (isInitialLoad) {
          state.loading = true;
        } else {
          state.categoryLoading = true;
        }
        state.error = null;
      })
      .addCase(fetchDeals.fulfilled, (state, action) => {
        state.loading = false;
        state.categoryLoading = false;
        state.deals = action.payload.deals;
        state.productCategories = action.payload.productCategories;
      })
      .addCase(fetchDeals.rejected, (state, action) => {
        state.loading = false;
        state.categoryLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearCategoryLoading } = dealsSlice.actions;
export default dealsSlice.reducer;
