import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { SingleSneakerService } from '@/services/singleSneaker';
import { SingleSneaker } from '@/types/singleSneaker';

interface SingleSneakerState {
  sneaker: SingleSneaker | null;
  loading: boolean;
  error: string | null;
}

const initialState: SingleSneakerState = {
  sneaker: null,
  loading: false,
  error: null,
};

export const fetchSingleSneaker = createAsyncThunk(
  'singleSneaker/fetchSingleSneaker',
  async (productId: string, { rejectWithValue }) => {
    try {
      return await SingleSneakerService.getSingleSneaker(productId);
    } catch (error: unknown) {
      if (error instanceof Error) {
        return rejectWithValue(
          error.message || 'Failed to fetch sneaker details',
        );
      }

      return rejectWithValue('Failed to fetch sneaker details');
    }
  },
);

const singleSneakerSlice = createSlice({
  name: 'singleSneaker',
  initialState,
  reducers: {},
  extraReducers: builder => {
    builder
      .addCase(fetchSingleSneaker.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSingleSneaker.fulfilled, (state, action) => {
        state.sneaker = action.payload;
        state.loading = false;
      })
      .addCase(fetchSingleSneaker.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export default singleSneakerSlice.reducer;
