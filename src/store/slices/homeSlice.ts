import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { HomeService } from '@/services/home';
import { UserService } from '@/services/user';
import { BalanceSummary, HomeResponse, UserProfile } from '@/types/home';

interface HomeState {
  data: HomeResponse | null;
  user: UserProfile | null;
  balance: BalanceSummary | null;
  loading: boolean;
  error: string | null;
}

const initialState: HomeState = {
  data: null,
  user: null,
  balance: null,
  loading: false,
  error: null,
};

export const fetchHomeData = createAsyncThunk(
  'home/fetchHomeData',
  async (_, { rejectWithValue }) => {
    try {
      return await HomeService.getHomeData();
    } catch (error: unknown) {
      if (error instanceof Error) {
        return rejectWithValue(error.message || 'Failed to fetch home data');
      }

      return rejectWithValue('Failed to fetch home data');
    }
  },
);

export const fetchUserProfile = createAsyncThunk(
  'home/fetchUserProfile',
  async (_, { rejectWithValue }) => {
    try {
      return await UserService.getUserData();
    } catch (error: unknown) {
      if (error instanceof Error) {
        return rejectWithValue(error.message || 'Failed to fetch user profile');
      }

      return rejectWithValue('Failed to fetch user profile');
    }
  },
);

export const fetchBalanceSummary = createAsyncThunk(
  'home/fetchBalanceSummary',
  async (_, { rejectWithValue }) => {
    try {
      return await HomeService.getBalanceSummary();
    } catch (error: unknown) {
      if (error instanceof Error) {
        return rejectWithValue(
          error.message || 'Failed to fetch balance summary',
        );
      }

      return rejectWithValue('Failed to fetch balance summary');
    }
  },
);

export const updateUserProfile = createAsyncThunk(
  'home/updateUserProfile',
  async (
    userData: {
      firstName: string;
      lastName: string;
      dateOfBirth: string;
      phone_number: string;
    },
    { rejectWithValue },
  ) => {
    try {
      return await UserService.updateUserProfile(userData);
    } catch (error: unknown) {
      if (error instanceof Error) {
        return rejectWithValue(
          error.message || 'Failed to update user profile',
        );
      }

      return rejectWithValue('Failed to update user profile');
    }
  },
);

const homeSlice = createSlice({
  name: 'home',
  initialState,
  reducers: {},
  extraReducers: builder => {
    builder
      .addCase(fetchHomeData.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchHomeData.fulfilled, (state, action) => {
        state.data = action.payload;
        state.loading = false;
      })
      .addCase(fetchHomeData.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchUserProfile.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUserProfile.fulfilled, (state, action) => {
        state.user = action.payload;
        state.loading = false;
      })
      .addCase(fetchUserProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchBalanceSummary.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchBalanceSummary.fulfilled, (state, action) => {
        state.balance = action.payload;
        state.loading = false;
      })
      .addCase(fetchBalanceSummary.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(updateUserProfile.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateUserProfile.fulfilled, (state, action) => {
        // Update the user data in the state
        if (state.user) {
          state.user = { ...state.user, ...action.payload };
        }
        state.loading = false;
      })
      .addCase(updateUserProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export default homeSlice.reducer;
export { homeSlice };
