import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { fetchStoreDetail as fetchStoreDetailService } from '@/services/cashback';
import { fetchStores, HomeService } from '@/services/home';
import { CashbackState } from '@/types/cashback';

const initialState: CashbackState = {
  stores: [],
  featuredStores: [],
  storeDetail: null,
  homeData: null,
  loading: false,
  featuredStoresLoading: false,
  error: null,
  featuredStoresError: null,
};

export const fetchCashbackStores = createAsyncThunk(
  'cashback/fetchStores',
  async (
    params: { page?: number; perPage?: number } = {},
    { rejectWithValue },
  ) => {
    try {
      const res = await fetchStores({
        perPage: params.perPage || 10,
        page: params.page || 1,
        visible: true,
        active: true,
      });

      return res.data;
    } catch (error: unknown) {
      if (error instanceof Error) {
        return rejectWithValue(error.message || 'Failed to fetch stores');
      }

      return rejectWithValue('Failed to fetch stores');
    }
  },
);

export const fetchFeaturedStores = createAsyncThunk(
  'cashback/fetchFeaturedStores',
  async (
    params: { page?: number; perPage?: number } = {},
    { rejectWithValue },
  ) => {
    try {
      const res = await fetchStores({
        perPage: params.perPage || 10,
        page: params.page || 1,
        visible: true,
        active: true,
        featured: true,
      });

      return res.data;
    } catch (error: unknown) {
      if (error instanceof Error) {
        return rejectWithValue(
          error.message || 'Failed to fetch featured stores',
        );
      }

      return rejectWithValue('Failed to fetch featured stores');
    }
  },
);

export const fetchStoreDetail = createAsyncThunk(
  'cashback/fetchStoreDetail',
  async (storeId: string, { rejectWithValue }) => {
    try {
      const response = await fetchStoreDetailService(storeId);

      return response;
    } catch (error: unknown) {
      if (error instanceof Error) {
        return rejectWithValue(error.message || 'Failed to fetch store detail');
      }

      return rejectWithValue('Failed to fetch store detail');
    }
  },
);

export const fetchCashbackHomeData = createAsyncThunk(
  'cashback/fetchHomeData',
  async (_, { rejectWithValue }) => {
    try {
      const response = await HomeService.getHomeData();

      return response;
    } catch (error: unknown) {
      if (error instanceof Error) {
        return rejectWithValue(error.message || 'Failed to fetch home data');
      }

      return rejectWithValue('Failed to fetch home data');
    }
  },
);

const cashbackSlice = createSlice({
  name: 'cashback',
  initialState,
  reducers: {},
  extraReducers: builder => {
    builder
      .addCase(fetchCashbackStores.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCashbackStores.fulfilled, (state, action) => {
        state.loading = false;

        // If it's the first page, replace stores, otherwise append
        if (action.meta.arg?.page === 1 || !action.meta.arg?.page) {
          state.stores = action.payload || [];
        } else {
          state.stores = [...state.stores, ...(action.payload || [])];
        }
      })
      .addCase(fetchCashbackStores.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchFeaturedStores.pending, state => {
        state.featuredStoresLoading = true;
        state.featuredStoresError = null;
      })
      .addCase(fetchFeaturedStores.fulfilled, (state, action) => {
        state.featuredStoresLoading = false;

        // If it's the first page, replace featured stores, otherwise append
        if (action.meta.arg?.page === 1 || !action.meta.arg?.page) {
          state.featuredStores = action.payload || [];
        } else {
          state.featuredStores = [
            ...state.featuredStores,
            ...(action.payload || []),
          ];
        }
      })
      .addCase(fetchFeaturedStores.rejected, (state, action) => {
        state.featuredStoresLoading = false;
        state.featuredStoresError = action.payload as string;
      })
      .addCase(fetchStoreDetail.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchStoreDetail.fulfilled, (state, action) => {
        state.loading = false;
        state.storeDetail = action.payload;
      })
      .addCase(fetchStoreDetail.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchCashbackHomeData.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCashbackHomeData.fulfilled, (state, action) => {
        state.loading = false;
        state.homeData = action.payload;
      })
      .addCase(fetchCashbackHomeData.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export default cashbackSlice.reducer;
