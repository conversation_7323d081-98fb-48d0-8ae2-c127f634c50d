import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { SneakersService } from '@/services/sneakers';
import { Sneaker } from '@/types/sneakers';

interface SneakersState {
  sneakers: Sneaker[];
  productCategories: string[];
  loading: boolean;
  error: string | null;
}

const initialState: SneakersState = {
  sneakers: [],
  productCategories: [],
  loading: false,
  error: null,
};

export const fetchSneakers = createAsyncThunk(
  'sneakers/fetchSneakers',
  async (_, { rejectWithValue }) => {
    try {
      const data = await SneakersService.getSneakers();

      // Extract unique categories from all sneakers
      const allCategories = new Set<string>();
      data.forEach(sneaker => {
        if (sneaker.categories) {
          sneaker.categories.forEach(category => {
            allCategories.add(category);
          });
        }
      });

      return {
        sneakers: data,
        productCategories: Array.from(allCategories).sort(),
      };
    } catch (error: unknown) {
      if (error instanceof Error) {
        return rejectWithValue(error.message || 'Failed to fetch sneakers');
      }

      return rejectWithValue('Failed to fetch sneakers');
    }
  },
);

const sneakersSlice = createSlice({
  name: 'sneakers',
  initialState,
  reducers: {},
  extraReducers: builder => {
    builder
      .addCase(fetchSneakers.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSneakers.fulfilled, (state, action) => {
        state.sneakers = action.payload.sneakers;
        state.productCategories = action.payload.productCategories;
        state.loading = false;
      })
      .addCase(fetchSneakers.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export default sneakersSlice.reducer;
