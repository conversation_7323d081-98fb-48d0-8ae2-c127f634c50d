import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { COMMON_ERROR } from '@/constants/common';
import { SignupFormData } from '@/screens/signup';
import { AuthService } from '@/services/auth';
import { UserService } from '@/services/user';
import { setItemToAS, STORAGE_KEYS } from '@/utils/storage';

interface UserData {
  user_id: string;
  discord_id: string | null;
  email: string;
  role: string;
  avatar_url: string | null;
  referred_by: string | null;
  referral_percent: string | null;
  date_of_birth: string | null;
  first_name: string | null;
  last_name: string | null;
  discord_nickname: string | null;
  phone_number: string | null;
  has_password: boolean;
  balance: string | null;
  invite_code: string;
}

interface AuthState {
  user: UserData;
  access_token: string | null;
  userDetails: {} | null;
  otherUserDetails: {} | null;
  status: 'idle' | 'loading' | 'succeeded' | 'failed';
  error: string | null;
}

const initialState: AuthState = {
  user: {
    user_id: '',
    discord_id: null,
    email: '',
    role: '',
    avatar_url: null,
    referred_by: null,
    referral_percent: null,
    date_of_birth: null,
    first_name: null,
    last_name: null,
    discord_nickname: null,
    phone_number: null,
    has_password: false,
    balance: null,
    invite_code: '',
  },
  access_token: null,
  userDetails: null,
  otherUserDetails: null,
  status: 'idle',
  error: null,
};

export const signUp = createAsyncThunk<
  {
    access_token: string;
    token_type: string;
  },
  { data: SignupFormData }
>('auth/signUp', async ({ data }) => {
  const response = await AuthService.signUp(data);

  return response;
});

export const signIn = createAsyncThunk<
  {
    access_token: string;
    token_type: string;
  },
  { data: { email: string; password: string; rememberMe: boolean } }
>('auth/signIn', async ({ data }) => {
  const response = await AuthService.signIn(data);
  setItemToAS(STORAGE_KEYS.AUTH_TOKEN, response.access_token);

  return response;
});

export const signInWithGoogle = createAsyncThunk<{
  access_token: string;
  token_type: string;
}>('auth/signInWithGoogle', async () => {
  const response = await AuthService.signInWithGoogle();
  setItemToAS(STORAGE_KEYS.AUTH_TOKEN, response.access_token);

  return response;
});

export const fetchUserDetails = createAsyncThunk<UserData>(
  'auth/fetchUserDetails',
  async () => {
    const response = await UserService.getUserData();

    return response;
  },
);

export const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setUser: (state, action) => {
      state.user = {
        ...state.user,
        ...action.payload,
      };
    },
    clearUser: state => {
      state.user = initialState.user;
      state.access_token = null;
    },
  },
  extraReducers: builder => {
    // sign up
    builder.addCase(signUp.pending, state => {
      state.status = 'loading';
    });
    builder.addCase(signUp.fulfilled, (state, action) => {
      const { payload } = action;
      state.access_token = payload.access_token;
      state.status = 'succeeded';
    });
    builder.addCase(signUp.rejected, (state, action) => {
      state.status = 'failed';
      state.error = action.error.message ?? COMMON_ERROR;
    });

    // sign in
    builder.addCase(signIn.pending, state => {
      state.status = 'loading';
    });
    builder.addCase(signIn.fulfilled, (state, action) => {
      const { payload } = action;
      state.access_token = payload.access_token;
      state.status = 'succeeded';
    });
    builder.addCase(signIn.rejected, (state, action) => {
      state.status = 'failed';
      state.error = action.error.message ?? COMMON_ERROR;
    });

    // sign in with google
    builder.addCase(signInWithGoogle.pending, state => {
      state.status = 'loading';
    });
    builder.addCase(signInWithGoogle.fulfilled, (state, action) => {
      const { payload } = action;
      state.access_token = payload.access_token;
      state.status = 'succeeded';
    });
    builder.addCase(signInWithGoogle.rejected, (state, action) => {
      state.status = 'failed';
      state.error = action.error.message ?? COMMON_ERROR;
    });

    // fetch user details
    builder.addCase(fetchUserDetails.pending, state => {
      state.status = 'loading';
    });
    builder.addCase(fetchUserDetails.fulfilled, (state, action) => {
      const { payload } = action;
      state.user = payload;
      state.status = 'succeeded';
    });
    builder.addCase(fetchUserDetails.rejected, (state, action) => {
      state.status = 'failed';
      state.error = action.error.message ?? COMMON_ERROR;
    });
  },
});

export const { setUser, clearUser } = authSlice.actions;
