import { combineReducers } from '@reduxjs/toolkit';

import { authSlice } from './slices/authSlice';
import cashbackReducer from './slices/cashbackSlice';
import dealsReducer from './slices/dealsSlice';
import { homeSlice } from './slices/homeSlice';
import singleSneakerReducer from './slices/singleSneakerSlice';
import sneakersReducer from './slices/sneakersSlice';

const rootReducer = combineReducers({
  [authSlice.name]: authSlice.reducer,
  [homeSlice.name]: homeSlice.reducer,
  deals: dealsReducer,
  sneakers: sneakersReducer,
  singleSneaker: singleSneakerReducer,
  cashback: cashbackReducer,
});

export { rootReducer };
