import { SECTION_KEY } from '../types/home';

export const homeData = {
  userInfo: {
    name: '<PERSON>',
    welcome: 'Welcome Back',
    balance: '$230.75',
    approved: '$90.84',
    pending: '$140.45',
    avatar: require('../../assets/images/png/avatar.png'),
  },
  upcomingDrops: [
    {
      id: '1',
      name: 'Air Jordan 3 Retro "Cement Grey"',
      price: '$200',
      oldPrice: '$325',
      daysLeft: 2,
      image: require('../../assets/images/png/shoe1.png'), // Replace with your image path
    },
    {
      id: '2',
      name: 'LeBron XXII "Currency"',
      price: '$140',
      oldPrice: '$190',
      daysLeft: 7,
      image: require('../../assets/images/png/shoe2.png'),
    },
    {
      id: '3',
      name: 'LeBron XXII "Currency"',
      price: '$140',
      oldPrice: '$190',
      daysLeft: 7,
      image: require('../../assets/images/png/shoe3.png'),
    },
    {
      id: '4',
      name: 'LeBron XXII "Currency"',
      price: '$140',
      oldPrice: '$190',
      daysLeft: 7,
      image: require('../../assets/images/png/shoe1.png'),
    },
    // Add more items as needed
  ],
  cashbackOffers: [
    {
      id: '1',
      brand: 'Adidas',
      type: 'Shoe Company',
      cashback: 'Upto 10%',
      image: require('../../assets/images/png/addidas.png'),
    },
    {
      id: '2',
      brand: 'Puma',
      type: 'Apparel Company',
      cashback: 'Upto 5%',
      image: require('../../assets/images/png/puma.png'),
    },
    {
      id: '3',
      brand: 'Rebook',
      type: 'Apparel Company',
      cashback: 'Upto 5%',
      image: require('../../assets/images/png/rebook.png'),
    },
    {
      id: '4',
      brand: 'Samsung',
      type: 'Apparel Company',
      cashback: 'Upto 5%',
      image: require('../../assets/images/png/samsung.png'),
    },
    {
      id: '5',
      brand: 'Norton',
      type: 'Apparel Company',
      cashback: 'Upto 5%',
      image: require('../../assets/images/png/norton.png'),
    },
    {
      id: '6',
      brand: 'Boss',
      type: 'Apparel Company',
      cashback: 'Upto 5%',
      image: require('../../assets/images/png/boss.png'),
    },
    // Add more items as needed
  ],
  popularShops: [
    {
      id: '1',
      name: 'Falcon',
      image: require('../../assets/images/png/shop1.png'),
    },
    {
      id: '2',
      name: 'Nike',
      image: require('../../assets/images/png/shop2.png'),
    },
    {
      id: '3',
      name: 'Nike',
      image: require('../../assets/images/png/shop3.png'),
    },
    {
      id: '4',
      name: 'Nike',
      image: require('../../assets/images/png/shop4.png'),
    },
    {
      id: '5',
      name: 'Nike',
      image: require('../../assets/images/png/shop5.png'),
    },
    {
      id: '6',
      name: 'Nike',
      image: require('../../assets/images/png/shop1.png'),
    },
    {
      id: '7',
      name: 'Nike',
      image: require('../../assets/images/png/shop3.png'),
    },
    // Add more items as needed
  ],
  bannerImages: [
    require('../../assets/images/png/banner1.png'),
    require('../../assets/images/png/banner2.png'),
    require('../../assets/images/png/banner2.png'),
    // Add more images as needed
  ],
};

export const sections: SECTION_KEY[] = [
  SECTION_KEY.HEADER,
  SECTION_KEY.BANNER,
  SECTION_KEY.UPCOMING_DROPS,
  SECTION_KEY.CASHBACK_OFFERS,
  SECTION_KEY.POPULAR_SHOPS,
];
