import { REST_SUB_URL } from '@/constants/common/api';
import { SingleSneaker } from '@/types/singleSneaker';
import api, { handleApiError } from '@/utils/api';

export const SingleSneakerService = {
  getSingleSneaker: async (productId: string): Promise<SingleSneaker> => {
    try {
      const response = await api.get(
        `${REST_SUB_URL.SINGLE_SNEAKER}${productId}`,
      );

      return response.data;
    } catch (error: unknown) {
      handleApiError(error);
      throw error;
    }
  },
};
