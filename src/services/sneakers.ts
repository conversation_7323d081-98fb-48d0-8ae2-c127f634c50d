import { BASE_URL, REST_SUB_URL } from '@/constants/common/api';
import { Sneaker } from '@/types/sneakers';

const SNEAKERS_API_URL = `${BASE_URL}${REST_SUB_URL.SNEAKERS}`;

export const SneakersService = {
  getSneakers: async (): Promise<Sneaker[]> => {
    const res = await fetch(SNEAKERS_API_URL);
    if (!res.ok) throw new Error(`HTTP ${res.status}`);
    const json = await res.json();

    return json.data;
  },
};
