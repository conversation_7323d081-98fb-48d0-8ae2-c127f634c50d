import { GET_STORES, REST_SUB_URL } from '@/constants/common/api';
import api, { handleApiError } from '@/utils/api';
import { getItemFromAS, STORAGE_KEYS } from '@/utils/storage';

export const HomeService = {
  getHomeData: async () => {
    try {
      const token = await getItemFromAS(STORAGE_KEYS.AUTH_TOKEN);
      if (!token) {
        throw new Error('NO_AUTH_TOKEN');
      }
      const response = await api.get(REST_SUB_URL.HOME);

      return response.data;
    } catch (error: unknown) {
      handleApiError(error);
      throw error;
    }
  },
  getBalanceSummary: async () => {
    try {
      const token = await getItemFromAS(STORAGE_KEYS.AUTH_TOKEN);
      if (!token) {
        throw new Error('NO_AUTH_TOKEN');
      }
      const response = await api.get(REST_SUB_URL.BALANCE_SUMMARY);

      return response.data;
    } catch (error: unknown) {
      handleApiError(error);
      throw error;
    }
  },
};

export const fetchStores = async (params = {}) => {
  try {
    // params can include featured, categories, min_commission, max_commission, page, perPage, dashboard, extension, visible, active, generator
    const response = await api.get(GET_STORES, { params });

    return response.data;
  } catch (error: unknown) {
    console.error('fetchStores: API error:', error);
    handleApiError(error);
    throw error;
  }
};
