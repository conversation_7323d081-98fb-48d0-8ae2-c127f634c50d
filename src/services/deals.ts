import { BASE_URL, REST_SUB_URL } from '@/constants/common/api';
import api, { handleApiError } from '@/utils/api';
import { getItemFromAS, STORAGE_KEYS } from '@/utils/storage';

export const DealsService = {
  getDeals: async (categoryId?: string) => {
    try {
      const token = await getItemFromAS(STORAGE_KEYS.AUTH_TOKEN);
      if (!token) {
        throw new Error('NO_AUTH_TOKEN');
      }
      const params: Record<string, string> = {
        active_only: 'true',
        frontpage_only: 'false',
        hot_only: 'false',
        limit: '20',
        offset: '0',
      };
      if (categoryId) {
        params.categories = categoryId;
      }
      const response = await api.get(`${BASE_URL}${REST_SUB_URL.DEALS}`, {
        params,
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      return response.data;
    } catch (error: unknown) {
      handleApiError(error);
      throw error;
    }
  },
  getCategories: async () => {
    try {
      const token = await getItemFromAS(STORAGE_KEYS.AUTH_TOKEN);
      if (!token) {
        throw new Error('NO_AUTH_TOKEN');
      }
      const response = await api.get(REST_SUB_URL.CATEGORIES, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      return response.data;
    } catch (error: unknown) {
      handleApiError(error);
      throw error;
    }
  },
  getRegions: async () => {
    try {
      const token = await getItemFromAS(STORAGE_KEYS.AUTH_TOKEN);
      if (!token) {
        throw new Error('NO_AUTH_TOKEN');
      }
      const response = await api.get(REST_SUB_URL.REGIONS, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      return response.data;
    } catch (error: unknown) {
      handleApiError(error);
      throw error;
    }
  },
};
