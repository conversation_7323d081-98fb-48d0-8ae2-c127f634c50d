import { REST_SUB_URL } from '@/constants/common/api';
import api, { handleApiError } from '@/utils/api';
import { getItemFromAS, STORAGE_KEYS } from '@/utils/storage';

export const UserService = {
  getUserData: async () => {
    try {
      const token = await getItemFromAS(STORAGE_KEYS.AUTH_TOKEN);
      if (!token) {
        throw new Error('NO_AUTH_TOKEN');
      }
      const response = await api.get(REST_SUB_URL.GET_USER_DATA);

      return response.data;
    } catch (error: unknown) {
      handleApiError(error);
      throw error;
    }
  },

  updateUserProfile: async (userData: {
    firstName: string;
    lastName: string;
    dateOfBirth: string;
    phone_number: string;
  }) => {
    try {
      const token = await getItemFromAS(STORAGE_KEYS.AUTH_TOKEN);
      if (!token) {
        throw new Error('NO_AUTH_TOKEN');
      }

      const response = await api.post(REST_SUB_URL.UPDATE_USER, userData);

      return response.data;
    } catch (error: unknown) {
      handleApiError(error);
      throw error;
    }
  },
};
