import {
  GoogleSignin,
  isSuccessResponse,
} from '@react-native-google-signin/google-signin';

import { BASE_URL, REST_SUB_URL } from '@/constants/common/api';
import { SignupFormData } from '@/screens/signup';
import api, { handleApiError } from '@/utils/api';

export const AuthService = {
  signIn: async (data: { email: string; password: string }) => {
    try {
      const url = `${BASE_URL}${REST_SUB_URL.SIGNIN}`;
      const response = await api.post(url, data, {
        headers: {
          'User-Agent': 'Mozilla/5.0',
        },
      });

      return response.data;
    } catch (error) {
      handleApiError(error);
      throw error;
    }
  },

  signUp: async (data: SignupFormData) => {
    try {
      const url = `${BASE_URL}${REST_SUB_URL.SIGNUP}`;
      const response = await api.post(
        url,
        {
          email: data.email,
          password: data.password,
          referral: 'string',
        },
        {
          headers: {
            'User-Agent': 'Mozilla/5.0',
          },
        },
      );

      return response.data;
    } catch (error: unknown) {
      handleApiError(error);
      throw error;
    }
  },

  signInWithGoogle: async () => {
    try {
      console.log('Starting Google Sign-In...');

      // Ensure GoogleSignin is configured

      await GoogleSignin.hasPlayServices();

      const response = await GoogleSignin.signIn();

      if (isSuccessResponse(response)) {
        const { idToken } = response.data;
        const url = `${BASE_URL}/auth/google`;
        const apiResponse = await api.post(url, { idToken });

        return apiResponse.data;
      }
      throw new Error('Google sign in failed');
    } catch (error: unknown) {
      handleApiError(error);
      throw error;
    }
  },
};
