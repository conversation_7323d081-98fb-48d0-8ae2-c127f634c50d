import React, { useEffect, useState } from 'react';
import { Image, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { SafeAreaView } from 'react-native-safe-area-context';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/Feather';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import { Input, makeStyles, useTheme } from '@rneui/themed';

import { CameraIcon, EditIcon } from '@/constants/svgs';
import { AppDispatch, RootState } from '@/store';
import { fetchUserProfile, updateUserProfile } from '@/store/slices/homeSlice';
import { UserProfile } from '@/types/home';

const getUserInitials = (user: UserProfile | null) => {
  if (user?.first_name && user?.last_name) {
    return `${user.first_name.charAt(0)}${user.last_name.charAt(0)}`.toUpperCase();
  } else if (user?.first_name) {
    return user.first_name.charAt(0).toUpperCase();
  } else if (user?.last_name) {
    return user.last_name.charAt(0).toUpperCase();
  }

  return null;
};

const PersonalInformation = () => {
  const { theme } = useTheme();
  const styles = useStyles(theme);
  const navigation = useNavigation();
  const dispatch = useDispatch<AppDispatch>();
  const { user, loading } = useSelector((state: RootState) => state.home);

  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [dateOfBirth, setDateOfBirth] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');

  // Update form fields when user data changes
  useEffect(() => {
    if (user) {
      setFirstName(user.first_name || '');
      setLastName(user.last_name || '');
      setDateOfBirth(user.date_of_birth || '');
      setEmail(user.email || '');
      setPhone(user.phone_number || '');
    }
  }, [user]);

  const handleSaveChanges = async () => {
    try {
      // Validate required fields
      if (!firstName.trim() || !lastName.trim()) {
        return;
      }

      const updateData = {
        firstName: firstName.trim(),
        lastName: lastName.trim(),
        dateOfBirth: dateOfBirth.trim(),
        phone_number: phone.trim(),
      };

      await dispatch(updateUserProfile(updateData)).unwrap();

      // Fetch fresh user data after successful update
      await dispatch(fetchUserProfile());

      navigation.goBack();
    } catch (error) {
      // Error is handled by the thunk
    }
  };

  const handleCancel = () => {
    navigation.goBack();
  };

  const handleChangeEmail = () => {
    // TODO: Implement change email
  };

  const handleChangePhone = () => {
    // TODO: Implement change phone
  };

  return (
    <SafeAreaView edges={['bottom']} style={styles.container}>
      <SafeAreaView
        edges={['top', 'left', 'right']}
        style={styles.headerSafeArea}
      >
        <LinearGradient
          colors={[theme.colors.foreground, theme.colors.brownGradient]}
        >
          <View style={styles.headerContent}>
            <View style={styles.headerRow}>
              <TouchableOpacity
                style={styles.backButton}
                onPress={() => navigation.goBack()}
              >
                <Icon
                  name="chevron-left"
                  size={moderateScale(24)}
                  color={theme.colors.white}
                />
              </TouchableOpacity>
              <Text style={styles.headerTitle}>Personal Information</Text>
              <View style={styles.placeholder} />
            </View>
          </View>
        </LinearGradient>
      </SafeAreaView>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.card}>
          {/* Profile Picture Section */}
          <View style={styles.avatarSection}>
            <View style={styles.avatarContainer}>
              {user?.avatar_url ? (
                <Image
                  source={{ uri: user.avatar_url }}
                  style={styles.avatar}
                />
              ) : (
                <View style={styles.avatarPlaceholder}>
                  <Text style={styles.avatarText}>
                    {getUserInitials(user) || 'U'}
                  </Text>
                </View>
              )}
              <View style={styles.cameraIcon}>
                <CameraIcon />
              </View>
            </View>
          </View>

          {/* Form Fields */}
          <View style={styles.formSection}>
            <View style={styles.fieldContainer}>
              <Text style={styles.fieldLabel}>First Name</Text>
              <Input
                value={firstName}
                onChangeText={setFirstName}
                placeholder="Enter first name"
                containerStyle={styles.inputContainer}
                inputContainerStyle={styles.inputContainerStyle}
              />
            </View>

            <View style={styles.fieldContainer}>
              <Text style={styles.fieldLabel}>Last Name</Text>
              <Input
                value={lastName}
                onChangeText={setLastName}
                placeholder="Enter last name"
                containerStyle={styles.inputContainer}
                inputContainerStyle={styles.inputContainerStyle}
              />
            </View>

            <View style={styles.fieldContainer}>
              <Text style={styles.fieldLabel}>Date of Birth</Text>
              <Input
                value={dateOfBirth}
                onChangeText={setDateOfBirth}
                placeholder="Enter date of birth"
                containerStyle={styles.inputContainer}
                inputContainerStyle={styles.inputContainerStyle}
              />
            </View>

            <View style={styles.fieldContainer}>
              <Text style={styles.fieldLabel}>Email</Text>
              <Input
                value={email}
                onChangeText={setEmail}
                placeholder="Enter email"
                containerStyle={styles.inputContainer}
                inputContainerStyle={styles.inputContainerStyle}
                keyboardType="email-address"
                autoCapitalize="none"
              />
              <TouchableOpacity
                style={styles.changeButton}
                onPress={handleChangeEmail}
              >
                <EditIcon color={theme.colors.grey3} />
                <Text style={styles.changeButtonText}>Change Email</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.fieldContainer}>
              <Text style={styles.fieldLabel}>Phone</Text>
              <View style={styles.phoneContainer}>
                <View style={styles.flagContainer}>
                  <Text style={styles.flagText}>🇺🇸</Text>
                </View>
                <Input
                  value={phone}
                  onChangeText={text => {
                    const cleaned = text.replace(/[^0-9+\-)\s]/g, '');
                    setPhone(cleaned);
                  }}
                  placeholder="Enter phone number"
                  containerStyle={styles.phoneInputContainer}
                  inputContainerStyle={styles.inputContainerStyle}
                  keyboardType="phone-pad"
                  maxLength={15}
                />
              </View>
              <TouchableOpacity
                style={styles.changeButton}
                onPress={handleChangePhone}
              >
                <EditIcon color={theme.colors.grey3} />
                <Text style={styles.changeButtonText}>Change Phone</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Action Buttons */}
          <View style={styles.buttonSection}>
            <TouchableOpacity
              style={[styles.saveButton, loading && styles.saveButtonDisabled]}
              onPress={handleSaveChanges}
              disabled={loading}
            >
              <Text style={styles.saveButtonText}>
                {loading ? 'Saving...' : 'Save Changes'}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.cancelButton}
              onPress={handleCancel}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const useStyles = makeStyles(theme => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  headerSafeArea: {
    backgroundColor: theme.colors.foreground,
  },
  headerContent: {
    paddingBottom: verticalScale(30),
    paddingTop: verticalScale(10),
    paddingHorizontal: moderateScale(16),
    gap: verticalScale(10),
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    marginBottom: verticalScale(12),
  },
  backButton: {
    width: moderateScale(40),
    height: moderateScale(40),
    borderRadius: moderateScale(20),
    backgroundColor: `${theme.colors.white}20`,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    color: theme.colors.white,
    fontSize: moderateScale(20),
    fontWeight: '500',
    marginLeft: moderateScale(10),
  },
  placeholder: {
    width: moderateScale(40),
  },
  content: {
    flex: 1,
    paddingHorizontal: moderateScale(16),
    marginTop: verticalScale(-20),
  },
  card: {
    backgroundColor: theme.colors.white,
    borderRadius: moderateScale(12),
    paddingHorizontal: moderateScale(12),
    paddingVertical: verticalScale(20),
    boxShadow: `0px 1px 2px 0px ${theme.colors.grey3}`,
  },
  avatarSection: {
    alignItems: 'center',
    marginBottom: verticalScale(30),
  },
  avatarContainer: {
    position: 'relative',
  },
  avatar: {
    width: moderateScale(80),
    height: moderateScale(80),
    borderRadius: moderateScale(40),
  },
  avatarPlaceholder: {
    width: moderateScale(80),
    height: moderateScale(80),
    borderRadius: moderateScale(40),
    backgroundColor: theme.colors.warning,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: moderateScale(32),
    fontWeight: 'bold',
    color: theme.colors.white,
  },
  cameraIcon: {
    position: 'absolute',
    bottom: -moderateScale(3),
    right: -moderateScale(3),
    width: moderateScale(35),
    height: moderateScale(35),
    borderRadius: moderateScale(15),
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  formSection: {
    marginBottom: verticalScale(20),
  },
  fieldContainer: {
    marginBottom: verticalScale(20),
  },
  fieldLabel: {
    fontSize: moderateScale(14),
    color: theme.colors.grey1,
    marginBottom: verticalScale(2),
    paddingLeft: moderateScale(16),
  },
  inputContainer: {
    paddingHorizontal: 0,
  },
  inputContainerStyle: {
    borderWidth: 0,
    borderColor: theme.colors.grey5,
    borderRadius: moderateScale(8),
    paddingHorizontal: moderateScale(14),
    height: verticalScale(46),
  },
  phoneContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  flagContainer: {
    width: moderateScale(40),
    height: verticalScale(46),
    justifyContent: 'center',
    alignItems: 'center',
    borderRightWidth: 0,
    borderTopLeftRadius: moderateScale(8),
    borderBottomLeftRadius: moderateScale(8),
  },
  flagText: {
    fontSize: moderateScale(16),
  },
  phoneInputContainer: {
    flex: 1,
    paddingHorizontal: 0,
  },
  changeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: verticalScale(8),
    paddingHorizontal: moderateScale(12),
    borderRadius: moderateScale(25),
    borderWidth: 1,
    borderColor: theme.colors.grey4,
    backgroundColor: theme.colors.white,
    alignSelf: 'flex-start',
  },
  changeButtonText: {
    fontSize: moderateScale(14),
    color: theme.colors.black,
    marginLeft: moderateScale(8),
    fontWeight: '500',
  },
  buttonSection: {
    gap: verticalScale(12),
  },
  saveButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: moderateScale(50),
    paddingVertical: verticalScale(12),
    alignItems: 'center',
  },
  saveButtonDisabled: {
    backgroundColor: theme.colors.grey3,
  },
  saveButtonText: {
    fontSize: moderateScale(16),
    fontWeight: '600',
    color: theme.colors.white,
  },
  cancelButton: {
    backgroundColor: theme.colors.white,
    borderRadius: moderateScale(50),
    paddingVertical: verticalScale(12),
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.grey3,
  },
  cancelButtonText: {
    fontSize: moderateScale(16),
    fontWeight: '600',
    color: theme.colors.black,
  },
}));

export default PersonalInformation;
