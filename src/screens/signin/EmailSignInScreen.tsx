import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { SafeAreaView, TouchableOpacity, View } from 'react-native';
import { moderateScale, scale, verticalScale } from 'react-native-size-matters';
import { AppleIconLogo, DiscordIconLogo } from '@assets/images/svg';
import { GoogleIconLogo } from '@assets/images/svg/GoogleIconLogo';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import {
  CheckBox,
  Icon,
  Input,
  makeStyles,
  Text,
  useTheme,
} from '@rneui/themed';

import AppButton from '@/components/AppButton';
import { useAppDispatch } from '@/hooks/redux';
import { signIn } from '@/store/slices/authSlice';
import { AUTH_ROUTES, AuthStackNavigatorParamList } from '@/types/routes';

type EmailSignInScreenNavigationProp = NativeStackNavigationProp<
  AuthStackNavigatorParamList,
  keyof AuthStackNavigatorParamList
>;

const EmailSignInScreen = ({
  navigation,
}: {
  navigation: EmailSignInScreenNavigationProp;
}) => {
  const { theme } = useTheme();
  const { t } = useTranslation();
  const styles = useStyles(theme);
  const dispatch = useAppDispatch();

  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('Geeks@428!');
  const [rememberMe, setRememberMe] = useState(false);
  const [passwordVisible, setPasswordVisible] = useState(false);

  const handleLogin = () => {
    dispatch(signIn({ data: { email, password, rememberMe } }));
  };

  const handleForgotPassword = () => {
    navigation.navigate(AUTH_ROUTES.FORGOT_PASSWORD);
  };

  const handleRegister = () => {
    navigation.navigate(AUTH_ROUTES.SIGNUP);
  };

  const handleGoogleSignIn = () => {
    // Implement Google sign in
  };

  const handleAppleSignIn = () => {
    // Implement Apple sign in
  };

  const handleDiscordSignIn = () => {
    // Implement Discord sign in
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <View style={styles.headingContainer}>
          <Text style={styles.heading}>
            {t('emailSignIn.heading', 'Hi, Welcome Back')} 👋🏻
          </Text>
          <Text style={styles.subheading}>
            {t('emailSignIn.subheading', 'Sign in to your account')}
          </Text>
        </View>

        <View style={styles.form}>
          <Text style={styles.label}>{t('emailSignIn.email', 'Email')}</Text>
          <Input
            placeholder="<EMAIL>"
            value={email}
            onChangeText={setEmail}
            autoCapitalize="none"
            keyboardType="email-address"
          />

          <Text style={styles.label}>
            {t('emailSignIn.password', 'Password')}
          </Text>
          <Input
            placeholder="Enter your password"
            value={password}
            onChangeText={setPassword}
            secureTextEntry={!passwordVisible}
            rightIcon={
              <Icon
                name={passwordVisible ? 'eye' : 'eye-off'}
                type="feather"
                size={20}
                color={theme.colors.neutralGrey}
                onPress={() => setPasswordVisible(!passwordVisible)}
              />
            }
          />

          <View style={styles.rememberForgotContainer}>
            <CheckBox
              checked={rememberMe}
              onPress={() => setRememberMe(!rememberMe)}
              title={t('emailSignIn.rememberMe', 'Remember Me')}
              containerStyle={styles.checkboxContainer}
              textStyle={styles.checkboxText}
              checkedColor={theme.colors.primary}
              uncheckedColor={theme.colors.neutralGrey}
            />

            <TouchableOpacity onPress={handleForgotPassword}>
              <Text style={styles.forgotPassword}>
                {t('emailSignIn.forgotPassword', 'Forgot Password')}
              </Text>
            </TouchableOpacity>
          </View>

          <AppButton
            type="primary"
            size="large"
            onPress={handleLogin}
            containerStyle={styles.loginButtonContainer}
          >
            <Text style={styles.loginButtonText}>
              {t('emailSignIn.login', 'Login')}
            </Text>
          </AppButton>
        </View>

        {/* Divider */}
        <View style={styles.dividerContainer}>
          <View style={styles.divider} />
          <Text style={styles.dividerText}>
            {t('emailSignIn.orSignInWith', 'or Sign In with')}
          </Text>
          <View style={styles.divider} />
        </View>

        {/* Social Buttons */}
        <View style={styles.socialButtonsContainer}>
          <AppButton
            type="ghost"
            size="medium"
            onPress={handleGoogleSignIn}
            containerStyle={styles.socialButton}
          >
            <GoogleIconLogo width={24} height={24} />
          </AppButton>

          <AppButton
            type="ghost"
            size="medium"
            onPress={handleAppleSignIn}
            containerStyle={styles.socialButton}
          >
            <AppleIconLogo width={24} height={24} />
          </AppButton>

          <AppButton
            type="ghost"
            size="medium"
            onPress={handleDiscordSignIn}
            containerStyle={styles.socialButton}
          >
            <DiscordIconLogo width={24} height={24} />
          </AppButton>
        </View>

        {/* Register Link */}
        <View style={styles.registerContainer}>
          <Text style={styles.registerText}>
            {t('emailSignIn.dontHaveAccount', 'Dont have an account?')}
          </Text>
          <TouchableOpacity onPress={handleRegister}>
            <Text style={styles.registerLink}>
              {t('emailSignIn.register', 'Register')}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

const useStyles = makeStyles(theme => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  content: {
    flex: 1,
    paddingHorizontal: scale(24),
    alignItems: 'center',
    marginTop: verticalScale(18),
  },
  backButton: {
    position: 'absolute',
    top: verticalScale(6),
    left: scale(16),
    width: scale(40),
    height: scale(40),
    borderRadius: scale(20),
    backgroundColor:
      theme.mode === 'dark' ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.02)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  logo: {
    width: scale(120),
    height: verticalScale(60),
    marginTop: verticalScale(26),
    marginBottom: verticalScale(20),
  },
  headingContainer: {
    width: '100%',
    alignItems: 'center',
  },
  heading: {
    fontSize: moderateScale(34),
    fontWeight: '500',
    color: theme.colors.foreground,
  },
  subheading: {
    fontSize: moderateScale(16),
    color: theme.colors.neutralGrey,
  },
  form: {
    width: '100%',
    marginTop: verticalScale(20),
  },
  label: {
    fontSize: moderateScale(16),
    fontWeight: '500',
    color: theme.colors.foreground,
    marginBottom: verticalScale(8),
  },
  rememberForgotContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: verticalScale(24),
  },
  checkboxContainer: {
    backgroundColor: 'transparent',
    borderWidth: 0,
    padding: 0,
    margin: 0,
  },
  checkboxText: {
    fontSize: moderateScale(14),
    fontWeight: 'normal',
    color: theme.colors.foreground,
    marginLeft: scale(8),
  },
  forgotPassword: {
    fontSize: moderateScale(14),
    color: theme.colors.primary,
    textDecorationLine: 'underline',
  },
  loginButtonContainer: {
    width: '100%',
    marginBottom: verticalScale(24),
  },
  loginButtonText: {
    fontSize: moderateScale(16),
    fontWeight: '600',
    color: theme.colors.secondary,
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    marginBottom: verticalScale(24),
  },
  divider: {
    flex: 1,
    height: 1,
    backgroundColor: theme.colors.grey5,
  },
  dividerText: {
    paddingHorizontal: scale(16),
    color: theme.colors.neutralGrey,
    fontSize: moderateScale(14),
  },
  socialButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
    marginBottom: verticalScale(40),
  },
  socialButton: {
    width: scale(80),
    marginHorizontal: scale(8),
  },
  registerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  registerText: {
    fontSize: moderateScale(14),
    marginRight: scale(4),
  },
  registerLink: {
    fontSize: moderateScale(14),
    color: theme.colors.primary,
    fontWeight: '500',
    textDecorationLine: 'underline',
  },
}));

export default EmailSignInScreen;
