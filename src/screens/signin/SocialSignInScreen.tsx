import React from 'react';
import { Platform, SafeAreaView, StatusBar, View } from 'react-native';
import { moderateScale, scale, verticalScale } from 'react-native-size-matters';
import Toast from 'react-native-toast-message';
import {
  AppleIconLogo,
  DiscordIconLogo,
  SolebackLogo,
} from '@assets/images/svg';
import { GoogleIconLogo } from '@assets/images/svg/GoogleIconLogo';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { makeStyles, Text, useTheme } from '@rneui/themed';

import AppButton from '@/components/AppButton';
import { useAppDispatch } from '@/hooks/redux';
import { signInWithGoogle } from '@/store/slices/authSlice';
import { AUTH_ROUTES, AuthStackNavigatorParamList } from '@/types/routes';

export interface Slide {
  key: string;
  title: string;
  description: string;
  image: ReturnType<typeof require>;
}

interface SocialSignInScreenProps {
  navigation: NativeStackNavigationProp<AuthStackNavigatorParamList>;
}

// GoogleSignin.configure({
//   webClientId:
//     '',
// androidClientId: GOOGLE_ANDROID_CLIENT_ID,
// iosClientId: GOOGLE_IOS_CLIENT_ID,
//   scopes: ['profile', 'email'],
// });

function SocialSignInScreen({ navigation }: SocialSignInScreenProps) {
  const { theme } = useTheme();
  // const { t } = useTranslation();
  const styles = useStyles(theme);
  const dispatch = useAppDispatch();

  const handleGoogleSignIn = async () => {
    try {
      await dispatch(signInWithGoogle()).unwrap();
      // Navigation will handle redirect based on auth state
    } catch (error: unknown) {
      Toast.show({
        type: 'error',
        text1: 'Google Sign-In Failed',
        text2: (error as Error).message,
      });
    }
  };

  const handleAppleSignIn = () => {};

  const handleDiscordSignIn = () => {};

  const handleEmailSignIn = () => {
    navigation.navigate(AUTH_ROUTES.EMAIL_SIGNIN);
  };

  const handleTermsPress = () => {};

  const handlePrivacyPress = () => {};

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <SolebackLogo style={styles.logo} />

        <Text style={styles.heading}>
          Maximize Savings with Soleback: Cashback at 300+ Retailers!
        </Text>

        <Text style={styles.subheading}>
          Unlock the ultimate shopping experience! Get rewarded for every
          purchase.
        </Text>

        <AppButton type="ghost" size="large" onPress={handleGoogleSignIn}>
          <GoogleIconLogo style={styles.socialIcon} />
          <Text style={styles.socialButtonText}>Sign in with Google</Text>
        </AppButton>

        <AppButton type="ghost" size="large" onPress={handleAppleSignIn}>
          <AppleIconLogo style={styles.socialIcon} />
          <Text style={styles.socialButtonText}>Sign in with Apple</Text>
        </AppButton>
        <AppButton type="ghost" size="large" onPress={handleDiscordSignIn}>
          <DiscordIconLogo style={styles.socialIcon} />
          <Text style={styles.socialButtonText}>Sign in with Discord</Text>
        </AppButton>

        <View style={styles.dividerContainer}>
          <View style={styles.divider} />
          <Text style={styles.dividerText}>or</Text>
          <View style={styles.divider} />
        </View>

        <AppButton
          type="primary"
          size="large"
          title="Sign in with Email"
          onPress={handleEmailSignIn}
        />

        <View style={styles.termsContainer}>
          <Text style={styles.termsText}>
            By continuing you acknowledge and agree to soleback{' '}
            <Text style={styles.termsLink} onPress={handleTermsPress}>
              General Terms of Use
            </Text>{' '}
            and{' '}
            <Text style={styles.termsLink} onPress={handlePrivacyPress}>
              Privacy Policy
            </Text>
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
}

const useStyles = makeStyles(theme => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
    paddingBottom: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  content: {
    flex: 1,
    paddingHorizontal: scale(20),
    justifyContent: 'space-between',
  },
  logo: {
    marginTop: verticalScale(10),
    marginHorizontal: 'auto',
  },
  heading: {
    fontSize: moderateScale(36),
    lineHeight: moderateScale(40),
    fontWeight: '500',
    marginTop: verticalScale(16),
    color: theme.colors.foreground,
  },
  subheading: {
    fontSize: moderateScale(16),
    textAlign: 'center',
    marginVertical: verticalScale(16),
    color: theme.colors.neutralGrey,
    lineHeight: moderateScale(24),
  },
  socialIcon: {
    marginRight: scale(10),
  },
  socialButtonText: {
    fontSize: moderateScale(16),
    color: theme.colors.foreground,
    fontWeight: '500',
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    marginVertical: verticalScale(15),
  },
  divider: {
    flex: 1,
    height: 1,
    backgroundColor:
      theme.mode === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
  },
  dividerText: {
    paddingHorizontal: scale(15),
    color: theme.colors.neutralGrey,
    fontSize: moderateScale(14),
  },
  emailButton: {
    width: '100%',
    height: verticalScale(50),
    borderRadius: moderateScale(25),
    backgroundColor: theme.colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: verticalScale(5),
  },
  termsContainer: {
    marginTop: verticalScale(10),
    paddingHorizontal: scale(20),
  },
  termsText: {
    fontSize: moderateScale(12),
    textAlign: 'center',
    color: theme.colors.neutralGrey,
    lineHeight: moderateScale(18),
  },
  termsLink: {
    color: theme.colors.primary,
    fontWeight: '500',
  },
}));

export default SocialSignInScreen;
