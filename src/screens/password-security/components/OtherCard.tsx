import React from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/Feather';
import { makeStyles, useTheme } from '@rneui/themed';

import { FollowUsIcon, PrivacyPolicyIcon } from '@/constants/svgs';

interface SettingItemProps {
  icon?: React.ReactNode;
  title: string;
  onPress: () => void;
  isLast?: boolean;
}

const SettingItem: React.FC<SettingItemProps> = ({
  icon,
  title,
  onPress,
  isLast = false,
}) => {
  const { theme } = useTheme();
  const styles = useStyles(theme);

  return (
    <TouchableOpacity style={styles.settingItem} onPress={onPress}>
      <View style={styles.settingContent}>
        <View style={styles.iconContainer}>{icon}</View>
        <View style={styles.textContainer}>
          <Text style={styles.settingTitle}>{title}</Text>
        </View>
        <Icon
          name="chevron-right"
          size={moderateScale(16)}
          color={theme.colors.grey0}
        />
      </View>
      {!isLast && <View style={styles.divider} />}
    </TouchableOpacity>
  );
};

interface OtherCardProps {
  onSharePress: () => void;
  onFollowUsPress: () => void;
  onPrivacyPolicyPress: () => void;
}

const OtherCard: React.FC<OtherCardProps> = ({
  onSharePress,
  onFollowUsPress,
  onPrivacyPolicyPress,
}) => {
  const { theme } = useTheme();
  const styles = useStyles(theme);

  return (
    <View style={styles.container}>
      <Text style={styles.sectionTitle}>Others</Text>
      <View style={styles.card}>
        <SettingItem
          icon={
            <Icon
              name="share-2"
              size={moderateScale(20)}
              color={theme.colors.grey1}
            />
          }
          title="Share"
          onPress={onSharePress}
        />
        <SettingItem
          icon={<FollowUsIcon color={theme.colors.grey1} />}
          title="Follow us"
          onPress={onFollowUsPress}
        />
        <SettingItem
          icon={<PrivacyPolicyIcon color={theme.colors.grey1} />}
          title="Privacy policy"
          onPress={onPrivacyPolicyPress}
          isLast
        />
      </View>
    </View>
  );
};

const useStyles = makeStyles(theme => ({
  container: {
    marginTop: verticalScale(20),
    marginHorizontal: moderateScale(20),
  },
  sectionTitle: {
    fontSize: moderateScale(16),
    fontWeight: '500',
    color: theme.colors.black,
    marginBottom: verticalScale(12),
  },
  card: {
    backgroundColor: theme.colors.white,
    borderRadius: moderateScale(12),
    padding: moderateScale(16),
    shadowColor: theme.colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  settingItem: {
    paddingVertical: verticalScale(12),
  },
  settingContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: moderateScale(4),
  },
  iconContainer: {
    width: moderateScale(24),
    alignItems: 'center',
    marginRight: moderateScale(12),
  },
  textContainer: {
    flex: 1,
  },
  settingTitle: {
    fontSize: moderateScale(14),
    fontWeight: '500',
    color: theme.colors.black,
  },
  divider: {
    height: 1,
    backgroundColor: theme.colors.grey4,
    marginTop: verticalScale(12),
  },
}));

export default OtherCard;
