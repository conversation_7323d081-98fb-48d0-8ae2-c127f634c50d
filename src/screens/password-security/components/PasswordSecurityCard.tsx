import React from 'react';
import { Switch, Text, TouchableOpacity, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/Feather';
import { makeStyles, useTheme } from '@rneui/themed';

import {
  BiometricIcon,
  ConfidentialityIcon,
  PasswordIcon,
} from '@/constants/svgs';

interface SettingItemProps {
  icon?: React.ReactNode;
  title: string;
  onPress: () => void;
  isLast?: boolean;
  showToggle?: boolean;
  toggleValue?: boolean;
  onToggleChange?: (value: boolean) => void;
}

const SettingItem: React.FC<SettingItemProps> = ({
  icon,
  title,
  onPress,
  isLast = false,
  showToggle = false,
  toggleValue = false,
  onToggleChange,
}) => {
  const { theme } = useTheme();
  const styles = useStyles(theme);

  return (
    <TouchableOpacity style={styles.settingItem} onPress={onPress}>
      <View style={styles.settingContent}>
        <View style={styles.iconContainer}>{icon}</View>
        <View style={styles.textContainer}>
          <Text style={styles.settingTitle}>{title}</Text>
        </View>
        {showToggle ? (
          <Switch
            value={toggleValue}
            onValueChange={onToggleChange}
            trackColor={{
              false: theme.colors.grey4,
              true: theme.colors.primary,
            }}
            thumbColor={theme.colors.white}
          />
        ) : (
          <Icon
            name="chevron-right"
            size={moderateScale(16)}
            color={theme.colors.grey0}
          />
        )}
      </View>
      {!isLast && <View style={styles.divider} />}
    </TouchableOpacity>
  );
};

interface PasswordSecurityCardProps {
  onChangePasswordPress: () => void;
  onBiometricToggle: () => void;
  onConfidentialityPolicyPress: () => void;
}

const PasswordSecurityCard: React.FC<PasswordSecurityCardProps> = ({
  onChangePasswordPress,
  onBiometricToggle,
  onConfidentialityPolicyPress,
}) => {
  const { theme } = useTheme();
  const styles = useStyles(theme);
  const [biometricEnabled, setBiometricEnabled] = React.useState(false);

  const handleBiometricToggle = (value: boolean) => {
    setBiometricEnabled(value);
    onBiometricToggle();
  };

  return (
    <View style={styles.container}>
      <View style={styles.card}>
        <SettingItem
          icon={<PasswordIcon color={theme.colors.grey0} />}
          title="Change Password"
          onPress={onChangePasswordPress}
        />
        <SettingItem
          icon={<BiometricIcon color={theme.colors.grey0} />}
          title="Biometric Authentications"
          onPress={onBiometricToggle}
          showToggle
          toggleValue={biometricEnabled}
          onToggleChange={handleBiometricToggle}
        />
        <SettingItem
          icon={<ConfidentialityIcon color={theme.colors.grey0} />}
          title="Confidentiality Policy"
          onPress={onConfidentialityPolicyPress}
          isLast
        />
      </View>
    </View>
  );
};

const useStyles = makeStyles(theme => ({
  container: {
    marginTop: verticalScale(-20),
    zIndex: 100,
  },
  sectionTitle: {
    fontSize: moderateScale(16),
    fontWeight: '500',
    color: theme.colors.black,
    marginBottom: verticalScale(12),
  },
  card: {
    backgroundColor: theme.colors.white,
    borderRadius: moderateScale(12),
    padding: moderateScale(16),
    marginHorizontal: moderateScale(20),
    shadowColor: theme.colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  settingItem: {
    paddingVertical: verticalScale(12),
  },
  settingContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: moderateScale(4),
  },
  iconContainer: {
    width: moderateScale(24),
    alignItems: 'center',
    marginRight: moderateScale(12),
  },
  textContainer: {
    flex: 1,
  },
  settingTitle: {
    fontSize: moderateScale(14),
    fontWeight: '500',
    color: theme.colors.black,
  },
  divider: {
    height: 1,
    backgroundColor: theme.colors.grey4,
    marginTop: verticalScale(12),
  },
}));

export default PasswordSecurityCard;
