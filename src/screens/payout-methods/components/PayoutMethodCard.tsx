import React from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import { makeStyles, useTheme } from '@rneui/themed';

import { BankIcon, EditIcon } from '@/constants/svgs';
import { BankAccount } from '@/types/payoutMethods';

interface PayoutMethodCardProps {
  account: BankAccount;
  onEditPress: (account: BankAccount) => void;
  isLast?: boolean;
}

const PayoutMethodCard: React.FC<PayoutMethodCardProps> = ({
  account,
  onEditPress,
  isLast = false,
}) => {
  const { theme } = useTheme();
  const styles = useStyles(theme);

  return (
    <View style={styles.container}>
      <View style={styles.card}>
        <View style={styles.header}>
          <View style={styles.bankInfo}>
            <View style={styles.bankIcon}>
              <BankIcon color={theme.colors.grey0} />
            </View>
            <View style={styles.bankNameRow}>
              <Text style={styles.bankName}>{account.bankName}</Text>
              {account.isPrimary && (
                <View style={styles.primaryTag}>
                  <Text style={styles.primaryText}>Primary</Text>
                </View>
              )}
            </View>
          </View>
          <TouchableOpacity
            style={styles.editButton}
            onPress={() => onEditPress(account)}
          >
            <EditIcon color={theme.colors.grey0} />
            <Text style={styles.editText}>Edit</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.accountDetails}>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Account Number : </Text>
            <Text style={styles.detailValue}>{account.accountNumber}</Text>
          </View>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Routing : </Text>
            <Text style={styles.detailValue}>{account.routingNumber}</Text>
          </View>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>SWIFT : </Text>
            <Text style={styles.detailValue}>{account.swiftCode}</Text>
          </View>
        </View>
      </View>
      {!isLast && <View style={styles.spacer} />}
    </View>
  );
};

const useStyles = makeStyles(theme => ({
  container: {
    marginBottom: verticalScale(16),
  },
  card: {
    backgroundColor: theme.colors.white,
    borderRadius: moderateScale(12),
    padding: moderateScale(16),
    shadowColor: theme.colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: verticalScale(12),
  },
  bankInfo: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    flex: 1,
  },
  bankIcon: {
    width: moderateScale(32),
    height: moderateScale(32),
    borderWidth: moderateScale(1),
    borderColor: theme.colors.grey3,
    borderRadius: moderateScale(24),
    backgroundColor: theme.colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: verticalScale(8),
  },
  bankNameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  bankName: {
    fontSize: moderateScale(16),
    fontWeight: '600',
    color: theme.colors.black,
    marginRight: moderateScale(8),
  },
  primaryTag: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: moderateScale(8),
    paddingVertical: verticalScale(2),
    borderRadius: moderateScale(4),
  },
  primaryText: {
    fontSize: moderateScale(12),
    fontWeight: '500',
    color: theme.colors.white,
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: moderateScale(8),
    paddingVertical: verticalScale(4),
  },
  editText: {
    fontSize: moderateScale(14),
    fontWeight: '500',
    color: theme.colors.grey0,
    marginLeft: moderateScale(4),
  },
  accountDetails: {
    gap: verticalScale(4),
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  detailLabel: {
    fontSize: moderateScale(14),
    color: theme.colors.grey2,
    fontWeight: '400',
  },
  detailValue: {
    fontSize: moderateScale(14),
    color: theme.colors.grey2,
    fontWeight: '400',
    flex: 1,
  },
  spacer: {
    height: verticalScale(8),
  },
}));

export default PayoutMethodCard;
