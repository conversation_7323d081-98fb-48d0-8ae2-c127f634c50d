import React from 'react';
import { ScrollView, Text, TouchableOpacity, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { SafeAreaView } from 'react-native-safe-area-context';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/Feather';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { makeStyles, useTheme } from '@rneui/themed';

import { BankAccount } from '@/types/payoutMethods';
import { APP_ROUTES, AppStackNavigatorParamList } from '@/types/routes';

import PayoutMethodCard from './components/PayoutMethodCard';

const PayoutMethodsScreen = () => {
  const { theme } = useTheme();
  const styles = useStyles(theme);
  const navigation =
    useNavigation<NativeStackNavigationProp<AppStackNavigatorParamList>>();

  // Mock data for bank accounts
  const bankAccounts: BankAccount[] = [
    {
      id: '1',
      bankName: 'First Century Bank',
      accountNumber: '**************',
      routingNumber: '2444342',
      swiftCode: 'FCB0232LS',
      isPrimary: true,
    },
    {
      id: '2',
      bankName: 'Citi Bank',
      accountNumber: '**************',
      routingNumber: '2444342',
      swiftCode: 'FCB0232LS',
      isPrimary: false,
    },
    {
      id: '3',
      bankName: 'Bank of America',
      accountNumber: '**************',
      routingNumber: '2444342',
      swiftCode: 'FCB0232LS',
      isPrimary: false,
    },
    {
      id: '4',
      bankName: 'Bank of America',
      accountNumber: '**************',
      routingNumber: '2444342',
      swiftCode: 'FCB0232LS',
      isPrimary: false,
    },
  ];

  const handleEditPress = (account: BankAccount) => {
    navigation.navigate(APP_ROUTES.EDIT_PAYOUT_METHOD, { account });
  };

  return (
    <SafeAreaView edges={['bottom']} style={styles.container}>
      <SafeAreaView
        edges={['top', 'left', 'right']}
        style={styles.headerSafeArea}
      >
        <LinearGradient
          colors={[theme.colors.foreground, theme.colors.brownGradient]}
        >
          <View style={styles.headerContent}>
            <View style={styles.headerRow}>
              <TouchableOpacity
                style={styles.backButton}
                onPress={() => navigation.goBack()}
              >
                <Icon
                  name="chevron-left"
                  size={moderateScale(24)}
                  color={theme.colors.white}
                />
              </TouchableOpacity>
              <Text style={styles.headerTitle}>Payout Methods</Text>
              <View style={styles.placeholder} />
            </View>
          </View>
        </LinearGradient>
      </SafeAreaView>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {bankAccounts.map((account, index) => (
          <PayoutMethodCard
            key={account.id}
            account={account}
            onEditPress={handleEditPress}
            isLast={index === bankAccounts.length - 1}
          />
        ))}
      </ScrollView>
    </SafeAreaView>
  );
};

export default PayoutMethodsScreen;

const useStyles = makeStyles(theme => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  headerSafeArea: {
    backgroundColor: theme.colors.foreground,
  },
  headerContent: {
    paddingBottom: verticalScale(30),
    paddingTop: verticalScale(10),
    paddingHorizontal: moderateScale(16),
    gap: verticalScale(10),
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    marginBottom: verticalScale(12),
  },
  backButton: {
    width: moderateScale(40),
    height: moderateScale(40),
    borderRadius: moderateScale(20),
    backgroundColor: `${theme.colors.white}20`,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    color: theme.colors.white,
    fontSize: moderateScale(20),
    fontWeight: '500',
    marginLeft: moderateScale(10),
  },
  placeholder: {
    width: moderateScale(40),
  },
  scrollView: {
    flex: 1,
    marginTop: verticalScale(-40),
  },
  scrollContent: {
    paddingHorizontal: moderateScale(16),
    paddingTop: verticalScale(16),
    paddingBottom: verticalScale(16),
  },
}));
