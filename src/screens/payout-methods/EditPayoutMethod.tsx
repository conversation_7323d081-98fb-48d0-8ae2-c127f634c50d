import React, { useState } from 'react';
import { Text, TextInput, TouchableOpacity, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { SafeAreaView } from 'react-native-safe-area-context';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/Feather';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { makeStyles, useTheme } from '@rneui/themed';

import { AppButton } from '@/components/AppButton';
import { BankIcon } from '@/constants/svgs';
import { APP_ROUTES, AppStackNavigatorParamList } from '@/types/routes';

type EditPayoutMethodRouteProp = RouteProp<
  AppStackNavigatorParamList,
  APP_ROUTES.EDIT_PAYOUT_METHOD
>;

const EditPayoutMethodScreen = () => {
  const { theme } = useTheme();
  const styles = useStyles(theme);
  const navigation =
    useNavigation<NativeStackNavigationProp<AppStackNavigatorParamList>>();
  const route = useRoute<EditPayoutMethodRouteProp>();
  const { account } = route.params;

  const [formData, setFormData] = useState({
    bankName: account.bankName,
    accountNumber: account.accountNumber,
    routingNumber: account.routingNumber,
    swiftCode: account.swiftCode,
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSaveChanges = () => {
    // TODO: Implement save functionality
    console.log('Saving changes:', formData);
    navigation.goBack();
  };

  const handleCancel = () => {
    navigation.goBack();
  };

  return (
    <SafeAreaView edges={['bottom']} style={styles.container}>
      <SafeAreaView
        edges={['top', 'left', 'right']}
        style={styles.headerSafeArea}
      >
        <LinearGradient
          colors={[theme.colors.foreground, theme.colors.brownGradient]}
        >
          <View style={styles.headerContent}>
            <View style={styles.headerRow}>
              <TouchableOpacity
                style={styles.backButton}
                onPress={() => navigation.goBack()}
              >
                <Icon
                  name="chevron-left"
                  size={moderateScale(24)}
                  color={theme.colors.white}
                />
              </TouchableOpacity>
              <Text style={styles.headerTitle}>Edit Payout Method</Text>
              <View style={styles.placeholder} />
            </View>
          </View>
        </LinearGradient>
      </SafeAreaView>

      <View style={styles.modalContainer}>
        <View style={styles.modal}>
          <View style={styles.bankIconContainer}>
            <BankIcon
              width={moderateScale(24)}
              height={moderateScale(24)}
              color={theme.colors.black}
            />
          </View>

          <View style={styles.formContainer}>
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Bank Name</Text>
              <TextInput
                style={styles.textInput}
                value={formData.bankName}
                onChangeText={value => handleInputChange('bankName', value)}
                placeholder="Enter bank name"
                placeholderTextColor={theme.colors.grey3}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Account Number</Text>
              <TextInput
                style={styles.textInput}
                value={formData.accountNumber}
                onChangeText={value =>
                  handleInputChange('accountNumber', value)
                }
                placeholder="Enter account number"
                placeholderTextColor={theme.colors.grey3}
                keyboardType="numeric"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Routing</Text>
              <TextInput
                style={styles.textInput}
                value={formData.routingNumber}
                onChangeText={value =>
                  handleInputChange('routingNumber', value)
                }
                placeholder="Enter routing number"
                placeholderTextColor={theme.colors.grey3}
                keyboardType="numeric"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>SWIFT</Text>
              <TextInput
                style={styles.textInput}
                value={formData.swiftCode}
                onChangeText={value => handleInputChange('swiftCode', value)}
                placeholder="Enter SWIFT code"
                placeholderTextColor={theme.colors.grey3}
                autoCapitalize="characters"
              />
            </View>
          </View>

          <View style={styles.buttonContainer}>
            <AppButton
              type="primary"
              size="large"
              onPress={handleSaveChanges}
              containerStyle={styles.saveButton}
            >
              <Text style={styles.saveButtonText}>Save Changes</Text>
            </AppButton>

            <TouchableOpacity
              style={styles.cancelButton}
              onPress={handleCancel}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default EditPayoutMethodScreen;

const useStyles = makeStyles(theme => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  headerSafeArea: {
    backgroundColor: theme.colors.foreground,
  },
  headerContent: {
    paddingBottom: verticalScale(30),
    paddingTop: verticalScale(10),
    paddingHorizontal: moderateScale(16),
    gap: verticalScale(10),
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    marginBottom: verticalScale(12),
  },
  backButton: {
    width: moderateScale(40),
    height: moderateScale(40),
    borderRadius: moderateScale(20),
    backgroundColor: `${theme.colors.white}20`,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    color: theme.colors.white,
    fontSize: moderateScale(20),
    fontWeight: '500',
    marginLeft: moderateScale(10),
  },
  placeholder: {
    width: moderateScale(40),
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: moderateScale(16),
    marginTop: verticalScale(-55),
  },
  modal: {
    backgroundColor: theme.colors.white,
    borderRadius: moderateScale(12),
    padding: moderateScale(24),
    width: '100%',
    maxWidth: moderateScale(400),
    shadowColor: theme.colors.black,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
  bankIconContainer: {
    alignSelf: 'flex-start',
    marginBottom: verticalScale(24),
    padding: moderateScale(8),
    borderRadius: moderateScale(24),
    backgroundColor: theme.colors.white,
    borderWidth: moderateScale(1),
    borderColor: theme.colors.grey3,
  },
  formContainer: {
    gap: verticalScale(16),
    marginBottom: verticalScale(24),
  },
  inputGroup: {
    gap: verticalScale(8),
  },
  inputLabel: {
    fontSize: moderateScale(14),
    fontWeight: '500',
    color: theme.colors.black,
  },
  textInput: {
    borderWidth: moderateScale(1),
    borderColor: theme.colors.grey3,
    borderRadius: moderateScale(8),
    paddingHorizontal: moderateScale(12),
    paddingVertical: verticalScale(12),
    fontSize: moderateScale(14),
    color: theme.colors.black,
    backgroundColor: theme.colors.white,
  },
  buttonContainer: {
    gap: verticalScale(12),
  },
  saveButton: {
    marginBottom: verticalScale(8),
  },
  saveButtonText: {
    color: theme.colors.white,
    fontSize: moderateScale(16),
    fontWeight: '600',
  },
  cancelButton: {
    borderWidth: moderateScale(1),
    borderColor: theme.colors.grey3,
    borderRadius: moderateScale(30),
    paddingVertical: verticalScale(8),
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.white,
  },
  cancelButtonText: {
    color: theme.colors.black,
    fontSize: moderateScale(16),
    fontWeight: '500',
  },
}));
