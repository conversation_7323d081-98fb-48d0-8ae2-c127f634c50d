import React from 'react';
import { Image, Text, TouchableOpacity, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { SafeAreaView } from 'react-native-safe-area-context';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/Feather';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { makeStyles, useTheme } from '@rneui/themed';

import { AppStackNavigatorParamList } from '@/types/routes';

const NotificationScreen = () => {
  const { theme } = useTheme();
  const styles = useStyles(theme);
  const navigation =
    useNavigation<NativeStackNavigationProp<AppStackNavigatorParamList>>();

  return (
    <View style={styles.container}>
      <SafeAreaView
        edges={['top', 'left', 'right']}
        style={styles.headerSafeArea}
      >
        <LinearGradient
          colors={[theme.colors.foreground, theme.colors.brownGradient]}
        >
          <View style={styles.headerContent}>
            <View style={styles.headerRow}>
              <TouchableOpacity
                style={styles.backBtn}
                onPress={() => navigation.goBack()}
              >
                <Icon name="arrow-left" size={24} color={theme.colors.white} />
              </TouchableOpacity>
              <Text style={styles.headerTitle}>Notification</Text>
              <View style={styles.headerSpacer} />
            </View>
          </View>
        </LinearGradient>
      </SafeAreaView>

      {/* Main Content */}
      <View style={styles.content}>
        {/* Bell Icon */}
        <View style={styles.iconContainer}>
          <Image
            source={require('../../../assets/images/png/notification.png')}
            resizeMode="contain"
          />
        </View>

        {/* Placeholder Elements */}

        {/* Main Text */}
        <Text style={styles.mainText}>No Notification Yet</Text>

        {/* Descriptive Text */}
        <Text style={styles.descriptionText}>
          Keep track of important updates, cashback alerts, and trending news
          right here.
        </Text>
      </View>
    </View>
  );
};

const useStyles = makeStyles(theme => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  headerSafeArea: {
    backgroundColor: theme.colors.foreground,
  },
  headerContent: {
    paddingBottom: verticalScale(30),
    paddingTop: verticalScale(10),
    paddingHorizontal: moderateScale(16),
    gap: verticalScale(10),
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: verticalScale(12),
  },
  backBtn: {
    borderWidth: moderateScale(1),
    borderColor: `${theme.colors.white}80`,
    borderRadius: moderateScale(100),
    padding: moderateScale(8),
  },
  headerTitle: {
    color: theme.colors.white,
    fontSize: moderateScale(20),
    fontWeight: '500',
  },
  headerSpacer: {
    width: moderateScale(32),
  },
  content: {
    flex: 1,
    backgroundColor: theme.colors.white,
    marginTop: verticalScale(20),
    marginHorizontal: moderateScale(16),
    borderRadius: moderateScale(12),
    paddingHorizontal: moderateScale(20),
    paddingTop: verticalScale(60),
    alignItems: 'center',
  },
  iconContainer: {
    marginBottom: verticalScale(40),
  },
  placeholderItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: verticalScale(16),
  },
  placeholderCircle: {
    width: moderateScale(8),
    height: moderateScale(8),
    borderRadius: moderateScale(4),
    backgroundColor: theme.colors.grey4,
    marginRight: moderateScale(12),
  },
  placeholderRectangle: {
    height: verticalScale(12),
    flex: 1,
    backgroundColor: theme.colors.grey4,
    borderRadius: moderateScale(6),
  },
  mainText: {
    fontSize: moderateScale(20),
    fontWeight: '700',
    color: theme.colors.black,
    marginBottom: verticalScale(12),
    fontFamily: 'Inter',
  },
  descriptionText: {
    fontSize: moderateScale(14),
    color: theme.colors.grey2,
    textAlign: 'center',
    lineHeight: moderateScale(20),
    fontFamily: 'Inter',
    paddingHorizontal: moderateScale(20),
  },
}));

export default NotificationScreen;
