import React from 'react';
import { ScrollView, Text, TouchableOpacity, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { SafeAreaView } from 'react-native-safe-area-context';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/Feather';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { makeStyles, useTheme } from '@rneui/themed';

import { CrownIcon } from '@/constants/svgs';
import { AppStackNavigatorParamList } from '@/types/routes';

const RewardsScreen = () => {
  const { theme } = useTheme();
  const styles = useStyles(theme);
  const navigation =
    useNavigation<NativeStackNavigationProp<AppStackNavigatorParamList>>();

  return (
    <View style={styles.container}>
      <SafeAreaView
        edges={['top', 'left', 'right']}
        style={styles.headerSafeArea}
      >
        <LinearGradient
          colors={[theme.colors.foreground, theme.colors.brownGradient]}
        >
          <View style={styles.headerContent}>
            <View style={styles.headerRow}>
              <TouchableOpacity
                style={styles.backBtn}
                onPress={() => navigation.goBack()}
              >
                <Icon name="arrow-left" size={24} color={theme.colors.white} />
              </TouchableOpacity>
              <Text style={styles.headerTitle}>Rewards</Text>
              <TouchableOpacity style={styles.moreBtn}>
                <Icon
                  name="more-vertical"
                  size={24}
                  color={theme.colors.white}
                />
              </TouchableOpacity>
            </View>
          </View>
        </LinearGradient>
      </SafeAreaView>

      <View style={styles.rewardsSection}>
        <View style={styles.crownContainer}>
          <View style={styles.crownIcon}>
            <CrownIcon
              color={theme.colors.white}
              width={moderateScale(70)}
              height={moderateScale(70)}
            />
          </View>
        </View>

        <View style={styles.levelInfo}>
          <Text style={styles.levelTitle}>Dimond</Text>
          <Text style={styles.pointsText}>1,940 Pts</Text>
        </View>
      </View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Active Benefits */}
        <View style={styles.benefitsSection}>
          <Text style={styles.sectionTitle}>Active Benefits</Text>

          <View style={styles.benefitCard}>
            <View style={styles.benefitIconContainer}>
              <Icon name="shopping-bag" size={20} color={theme.colors.black} />
            </View>
            <Text style={styles.benefitText}>
              2% Extra Cashback on Sneakers
            </Text>
          </View>

          <View style={styles.benefitCard}>
            <View style={styles.benefitIconContainer}>
              <Icon name="dollar-sign" size={20} color={theme.colors.black} />
            </View>
            <Text style={styles.benefitText}>5% In Store Cashback</Text>
          </View>

          <View style={styles.benefitCard}>
            <View style={styles.benefitIconContainer}>
              <Icon name="gift" size={20} color={theme.colors.black} />
            </View>
            <Text style={styles.benefitText}>8% New release Item Sale</Text>
          </View>
        </View>

        {/* Next Level Section */}
        <View style={styles.nextLevelSection}>
          <Text style={styles.nextLevelTitle}>NEXT LEVEL</Text>
          <View style={styles.nextLevelHeader}>
            <Text style={styles.nextLevelName}>Platinum</Text>
            <Text style={styles.nextLevelPoints}>3000 Pts</Text>
          </View>

          <View style={styles.progressContainer}>
            <View style={styles.progressBar}>
              <View style={styles.progressFill} />
            </View>
            <Text style={styles.progressText}>
              You need 1,640 more points to go the next level
            </Text>
          </View>

          <View style={styles.nextLevelBenefits}>
            <View style={styles.benefitCard}>
              <View style={styles.benefitIconContainer}>
                <Icon
                  name="shopping-bag"
                  size={20}
                  color={theme.colors.black}
                />
              </View>
              <Text style={styles.benefitText}>
                Get extra 5% cashback on all store
              </Text>
            </View>

            <View style={styles.benefitCard}>
              <View style={styles.benefitIconContainer}>
                <Icon
                  name="shopping-bag"
                  size={20}
                  color={theme.colors.black}
                />
              </View>
              <Text style={styles.benefitText}>
                Get Exclusive 10% cashback on all selected store
              </Text>
            </View>
          </View>

          <TouchableOpacity style={styles.showMoreBtn}>
            <Text style={styles.showMoreText}>Show More</Text>
          </TouchableOpacity>
        </View>

        {/* Unlock More Button */}
        <TouchableOpacity style={styles.unlockButton}>
          <Text style={styles.unlockButtonText}>Unlock More</Text>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
};

const useStyles = makeStyles(theme => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.base,
  },
  headerSafeArea: {
    backgroundColor: theme.colors.foreground,
  },
  headerContent: {
    paddingBottom: verticalScale(30),
    paddingTop: verticalScale(10),
    paddingHorizontal: moderateScale(16),
    gap: verticalScale(10),
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: verticalScale(12),
  },
  backBtn: {
    borderWidth: moderateScale(1),
    borderColor: `${theme.colors.white}80`,
    borderRadius: moderateScale(100),
    padding: moderateScale(8),
  },
  moreBtn: {
    borderWidth: moderateScale(1),
    borderColor: `${theme.colors.white}80`,
    borderRadius: moderateScale(100),
    padding: moderateScale(8),
  },
  headerTitle: {
    color: theme.colors.white,
    fontSize: moderateScale(20),
    fontWeight: '500',
  },
  rewardsSection: {
    alignItems: 'center',
    marginTop: verticalScale(-30),
    marginBottom: verticalScale(10),
  },
  crownContainer: {
    marginBottom: verticalScale(20),
    borderWidth: moderateScale(5),
    borderColor: theme.colors.white,
    borderRadius: moderateScale(100),
  },
  crownIcon: {
    width: moderateScale(120),
    height: moderateScale(120),
    borderRadius: moderateScale(60),
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',

    shadowColor: theme.colors.black,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
    zIndex: 10,
  },
  levelInfo: {
    alignItems: 'center',
    paddingHorizontal: moderateScale(20),
  },
  levelTitle: {
    fontSize: moderateScale(24),
    fontWeight: '500',
    color: theme.colors.foreground,
    marginBottom: verticalScale(8),
    textAlign: 'center',
  },
  pointsText: {
    fontSize: moderateScale(16),
    color: theme.colors.grey2,
    fontWeight: '400',
    marginBottom: verticalScale(4),
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: verticalScale(20),
    paddingHorizontal: moderateScale(16),
  },
  benefitsSection: {
    marginBottom: verticalScale(40),
  },
  sectionTitle: {
    fontSize: moderateScale(18),
    fontWeight: '600',
    color: theme.colors.black,
    marginBottom: verticalScale(16),
    fontFamily: 'Inter',
  },
  benefitCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.white,
    paddingVertical: verticalScale(16),
    paddingHorizontal: moderateScale(16),
    borderRadius: moderateScale(12),
    marginBottom: verticalScale(12),
    shadowColor: theme.colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  benefitIconContainer: {
    width: moderateScale(40),
    height: moderateScale(40),
    borderRadius: moderateScale(20),
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: moderateScale(12),
  },
  benefitText: {
    fontSize: moderateScale(14),
    color: theme.colors.black,
    fontWeight: '500',
    flex: 1,
    fontFamily: 'Inter',
  },
  nextLevelSection: {
    marginBottom: verticalScale(40),
  },
  nextLevelTitle: {
    fontSize: moderateScale(14),
    color: theme.colors.primary,
    fontWeight: '500',
    marginBottom: verticalScale(12),
    fontFamily: 'Inter',
  },
  nextLevelHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: verticalScale(16),
  },
  nextLevelName: {
    fontSize: moderateScale(20),
    fontWeight: '700',
    color: theme.colors.black,
    fontFamily: 'Inter',
  },
  nextLevelPoints: {
    fontSize: moderateScale(14),
    color: theme.colors.grey2,
    fontWeight: '500',
    fontFamily: 'Inter',
  },
  progressContainer: {
    marginBottom: verticalScale(20),
  },
  progressBar: {
    height: verticalScale(8),
    backgroundColor: theme.colors.grey4,
    borderRadius: moderateScale(4),
    marginBottom: verticalScale(12),
  },
  progressFill: {
    height: '100%',
    width: '65%',
    backgroundColor: theme.colors.primary,
    borderRadius: moderateScale(4),
  },
  progressText: {
    fontSize: moderateScale(12),
    color: theme.colors.grey2,
    fontFamily: 'Inter',
  },
  nextLevelBenefits: {
    marginBottom: verticalScale(16),
  },
  showMoreBtn: {
    alignSelf: 'center',
  },
  showMoreText: {
    fontSize: moderateScale(14),
    color: theme.colors.primary,
    fontWeight: '500',
    fontFamily: 'Inter',
  },
  unlockButton: {
    backgroundColor: theme.colors.primary,
    paddingVertical: verticalScale(16),
    borderRadius: moderateScale(12),
    alignItems: 'center',
    marginTop: 'auto',
    marginBottom: verticalScale(20),
  },
  unlockButtonText: {
    fontSize: moderateScale(16),
    fontWeight: '600',
    color: theme.colors.black,
    fontFamily: 'Inter',
  },
}));

export default RewardsScreen;
