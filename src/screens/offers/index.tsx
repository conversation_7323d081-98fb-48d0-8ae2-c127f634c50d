import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  FlatList,
  Image,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { SafeAreaView } from 'react-native-safe-area-context';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/Feather';
import { useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { makeStyles, useTheme } from '@rneui/themed';

import { RootState } from '@/store';
import { PromotedOffer } from '@/types/home';
import { APP_ROUTES, AppStackNavigatorParamList } from '@/types/routes';

const AllOffersScreen = () => {
  const { theme } = useTheme();
  const styles = useStyles(theme);
  const navigation =
    useNavigation<NativeStackNavigationProp<AppStackNavigatorParamList>>();
  const { homeData } = useSelector((state: RootState) => state.cashback);

  const [offers, setOffers] = useState<PromotedOffer[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (homeData?.promoted_offers) {
      setOffers(homeData.promoted_offers);
      setLoading(false);
    }
  }, [homeData]);

  const renderItem = ({ item }: { item: PromotedOffer }) => (
    <TouchableOpacity
      style={styles.card}
      activeOpacity={0.9}
      onPress={() => {
        // Type assertion to match the expected deal type
        navigation.navigate(APP_ROUTES.DEAL_DETAIL, {
          deal: item as import('@/types/deals').PromotedOffer,
        });
      }}
    >
      <View style={styles.row}>
        <Image source={{ uri: item.store.logo }} style={styles.logo} />
        <View style={styles.info}>
          <Text style={styles.name}>{item.store.name}</Text>
          <Text style={styles.desc} numberOfLines={2}>
            {item.title}
          </Text>
          <View style={styles.offerBadge}>
            <Text style={styles.offerBadgeText}>
              {item.voucher.promotion_percent}% Cashback
            </Text>
          </View>
        </View>
        <Icon
          name="chevron-right"
          size={moderateScale(20)}
          color={theme.colors.foreground}
          style={styles.arrowIcon}
        />
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.container}>
        <SafeAreaView
          edges={['top', 'left', 'right']}
          style={styles.headerSafeArea}
        >
          <LinearGradient
            colors={[theme.colors.foreground, theme.colors.brownGradient]}
          >
            <View style={styles.headerContent}>
              <View style={styles.headerRow}>
                <TouchableOpacity
                  style={styles.backButton}
                  onPress={() => navigation.goBack()}
                >
                  <Icon
                    name="arrow-left"
                    size={moderateScale(24)}
                    color={theme.colors.white}
                  />
                </TouchableOpacity>
                <Text style={styles.headerTitle}>All Offers</Text>
                <View style={styles.placeholder} />
              </View>
            </View>
          </LinearGradient>
        </SafeAreaView>
        <View style={styles.loadingContainer}>
          <ActivityIndicator
            size="large"
            color={theme.colors.primary}
            style={styles.loadingSpinner}
          />
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <SafeAreaView
        edges={['top', 'left', 'right']}
        style={styles.headerSafeArea}
      >
        <LinearGradient
          colors={[theme.colors.foreground, theme.colors.brownGradient]}
        >
          <View style={styles.headerContent}>
            <View style={styles.headerRow}>
              <TouchableOpacity
                style={styles.backButton}
                onPress={() => navigation.goBack()}
              >
                <Icon name="arrow-left" size={24} color={theme.colors.white} />
              </TouchableOpacity>
              <Text style={styles.headerTitle}>All Offers</Text>
              <View style={styles.placeholder} />
            </View>
          </View>
        </LinearGradient>
      </SafeAreaView>

      <FlatList
        data={offers}
        renderItem={renderItem}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>No offers available</Text>
          </View>
        }
      />
    </View>
  );
};

const useStyles = makeStyles(theme => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  headerSafeArea: {
    backgroundColor: theme.colors.foreground,
  },
  headerContent: {
    paddingHorizontal: moderateScale(16),
    paddingVertical: moderateScale(12),
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  backButton: {
    padding: moderateScale(8),
  },
  headerTitle: {
    fontFamily: 'Inter',
    fontWeight: '600',
    fontSize: moderateScale(18),
    color: theme.colors.white,
  },
  placeholder: {
    flex: 1,
  },
  listContainer: {
    paddingBottom: 20,
  },
  card: {
    backgroundColor: theme.colors.background,
    borderRadius: moderateScale(4),
    marginHorizontal: moderateScale(16),
    marginVertical: verticalScale(8),
    padding: moderateScale(18),
    boxShadow: `0px 11.82px 22.61px 0px ${theme.colors.grey3}`,
    elevation: 2,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  logo: {
    width: moderateScale(44),
    height: verticalScale(44),
    borderRadius: moderateScale(8),
    marginRight: moderateScale(14),
    backgroundColor: theme.colors.neutralGrey,
    marginTop: verticalScale(2),
  },
  info: {
    flex: 1,
    flexDirection: 'column',
  },
  name: {
    fontWeight: '700',
    fontSize: moderateScale(16),
    color: theme.colors.foreground,
    marginBottom: verticalScale(2),
  },
  desc: {
    fontSize: moderateScale(13),
    color: theme.colors.neutralGrey,
    marginBottom: verticalScale(8),
    fontWeight: '400',
  },
  arrowIcon: {
    marginLeft: moderateScale(10),
    marginTop: verticalScale(6),
  },
  offerBadge: {
    backgroundColor: theme.colors.cashbackBg,
    borderWidth: 1,
    borderColor: theme.colors.cashbackBorder,
    borderRadius: moderateScale(6),
    paddingHorizontal: moderateScale(12),
    paddingVertical: verticalScale(4),
    alignSelf: 'flex-start',
    marginTop: 0,
  },
  offerBadgeText: {
    color: theme.colors.cashbackText,
    fontWeight: '600',
    fontSize: moderateScale(13),
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingSpinner: {
    marginBottom: verticalScale(10),
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: verticalScale(40),
  },
  emptyText: {
    fontSize: moderateScale(16),
    color: theme.colors.neutralGrey,
    textAlign: 'center',
  },
}));

export default AllOffersScreen;
