import React from 'react';
import { Image, Text, TouchableOpacity, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/Feather';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Card, makeStyles, useTheme } from '@rneui/themed';

import { APP_ROUTES, SneakersStackNavigatorParamList } from '@/types/routes';
import { Sneaker } from '@/types/sneakers';

interface SneakerCardProps {
  item: Sneaker | null;
}

const SneakerCard: React.FC<SneakerCardProps> = ({ item }) => {
  const styles = useStyles();
  const navigation =
    useNavigation<NativeStackNavigationProp<SneakersStackNavigatorParamList>>();
  const { theme } = useTheme();

  if (!item) {
    return <View style={styles.emptyCard} />;
  }

  // Get first price (show only one, no strikethrough)
  const price = item.retail_prices[0]
    ? `${item.retail_prices[0].currency} ${item.retail_prices[0].amount}`
    : '';

  return (
    <TouchableOpacity
      onPress={() =>
        navigation.navigate(APP_ROUTES.SINGLE_SNEAKER, {
          productId: item.product_id,
        })
      }
    >
      <Card
        containerStyle={styles.cardContainer}
        wrapperStyle={styles.cardWrapper}
      >
        <View style={styles.cardImageContainer}>
          <Image
            source={{
              uri:
                item.images.find(img => img.is_main_image)?.image_url ||
                item.images[0]?.image_url,
            }}
            style={styles.cardImage}
          />
        </View>
        <View style={styles.cardInfo}>
          <Text style={styles.cardBrandLabel}>{item.brand}</Text>
          <Text style={styles.cardTitle} numberOfLines={2}>
            {item.model}
          </Text>
          <View style={styles.cardFooterRow}>
            <Text style={styles.cardPrice}>{price}</Text>
            <TouchableOpacity style={styles.cardArrowBtn}>
              <Icon name="chevron-right" size={24} color={theme.colors.black} />
            </TouchableOpacity>
          </View>
        </View>
      </Card>
    </TouchableOpacity>
  );
};

const useStyles = makeStyles(theme => ({
  emptyCard: {
    flex: 1,
    marginHorizontal: moderateScale(4),
    backgroundColor: 'transparent',
  },
  cardContainer: {
    borderRadius: moderateScale(12),
    flex: 1,
    marginHorizontal: moderateScale(4),
    marginBottom: verticalScale(16),
    elevation: 2,
    shadowColor: theme.colors.black,
    shadowOffset: { width: 0, height: verticalScale(2) },
    shadowOpacity: 0.1,
    shadowRadius: moderateScale(4),
    padding: 0,
  },
  cardWrapper: {
    margin: 0,
    padding: 0,
  },
  cardImageContainer: {
    width: '100%',
    height: moderateScale(110),
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.white,
    marginTop: verticalScale(4),
  },
  cardImage: {
    width: '95%',
    height: '100%',
    resizeMode: 'cover',
    borderRadius: moderateScale(8),
  },
  cardInfo: {
    padding: moderateScale(12),
  },
  cardBrandLabel: {
    fontSize: moderateScale(10),
    color: theme.colors.grey2,
    marginBottom: verticalScale(2),
  },
  cardTitle: {
    fontSize: moderateScale(14),
    fontWeight: '600',
    color: theme.colors.black,
    marginBottom: verticalScale(8),
  },
  cardFooterRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: verticalScale(4),
  },
  cardPrice: {
    color: theme.colors.primary,
    fontWeight: '600',
    fontSize: moderateScale(14),
    marginRight: moderateScale(8),
  },
  cardArrowBtn: {
    width: moderateScale(30),
    height: moderateScale(30),
    borderRadius: moderateScale(22),
    borderWidth: 1,
    borderColor: `${theme.colors.black}14`,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 'auto',
    backgroundColor: theme.colors.white,
  },
}));

export default SneakerCard;
