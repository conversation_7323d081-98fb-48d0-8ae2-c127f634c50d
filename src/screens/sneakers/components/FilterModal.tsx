import React, { useEffect, useState } from 'react';
import { ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/Feather';
import { useSelector } from 'react-redux';
import MultiSlider from '@ptomasroos/react-native-multi-slider';
import { CheckBox, makeStyles, useTheme } from '@rneui/themed';

import {
  FilterIconOutline,
  MultiSlider as MultiSliderIcon,
} from '@/constants/svgs';
import { DealsService } from '@/services/deals';
import { RootState } from '@/store';
import { Region } from '@/types/deals';

const BAR_RANGES = [0, 60, 120, 180, 240, 300]; // 0-60, 60-120, ...
const DUMMY_PRODUCT_COUNTS = [8, 15, 23, 6, 12]; // Example product counts for each range
const BAR_MAX_COUNT = Math.max(...DUMMY_PRODUCT_COUNTS);

const SliderMarker = () => (
  <MultiSliderIcon width={moderateScale(32)} height={verticalScale(32)} />
);

type FilterModalHeaderProps = {
  onReset?: () => void;
  theme: ReturnType<typeof useTheme>['theme'];
  styles: ReturnType<typeof useStyles>;
};

const FilterModalHeader: React.FC<FilterModalHeaderProps> = ({
  onReset,
  theme,
  styles,
}) => (
  <View style={styles.headerContainer}>
    <View style={styles.dragIndicator} />
    <View style={styles.headerRow}>
      <View style={styles.headerTitleRow}>
        <FilterIconOutline
          color={theme.colors.grey1}
          style={styles.headerIcon}
        />
        <Text style={styles.headerTitle}>Filter</Text>
      </View>
      <TouchableOpacity onPress={onReset} style={styles.resetBtn}>
        <Text style={styles.resetText}>Reset</Text>
      </TouchableOpacity>
    </View>
    <View style={styles.headerDivider} />
  </View>
);

const FilterModal: React.FC<{ onClose?: () => void }> = ({ onClose }) => {
  const { productCategories } = useSelector(
    (state: RootState) => state.sneakers,
  );

  const [cashbackChecks, setCashbackChecks] = useState({
    under25: false,
    between25_50: false,
    between50_100: false,
  });
  const [storeTypeChecks, setStoreTypeChecks] = useState<
    Record<string, boolean>
  >({});
  const [regions, setRegions] = useState<Region[]>([]);
  const [selectedRegion, setSelectedRegion] = useState<string>('');
  const [cashbackRange, setCashbackRange] = useState<[number, number]>([
    25, 100,
  ]);
  const [sliderWidth, setSliderWidth] = useState(0);
  const [barChartWidth, setBarChartWidth] = useState(0);
  const [isLoadingRegions, setIsLoadingRegions] = useState(true);

  const toggleCashback = (key: keyof typeof cashbackChecks) => {
    setCashbackChecks(prev => ({ ...prev, [key]: !prev[key] }));
  };
  const toggleStoreType = (key: string) => {
    setStoreTypeChecks(prev => ({ ...prev, [key]: !prev[key] }));
  };

  const { theme } = useTheme();
  const styles = useStyles(theme);

  useEffect(() => {
    const initialChecks: Record<string, boolean> = {};
    productCategories.forEach(category => {
      initialChecks[category] = false;
    });
    setStoreTypeChecks(initialChecks);
  }, [productCategories]);

  useEffect(() => {
    setIsLoadingRegions(true);
    DealsService.getRegions()
      .then(data => {
        setRegions(data);
        if (data && data.length > 0) {
          setSelectedRegion(data[0].region_id);
        }
      })
      .catch(error => {
        console.error('Failed to load regions:', error);
        setRegions([]);
      })
      .finally(() => {
        setIsLoadingRegions(false);
      });
  }, []);

  const isBarInRange = (barIdx: number) => {
    const barMin = BAR_RANGES[barIdx];
    const barMax = BAR_RANGES[barIdx + 1];

    return cashbackRange[0] < barMax && cashbackRange[1] > barMin;
  };

  return (
    <View style={styles.container}>
      <FilterModalHeader onReset={onClose} theme={theme} styles={styles} />
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Offer Region */}
        <View style={styles.section}>
          <View style={styles.sectionHeaderRow}>
            <Text style={styles.sectionTitleBlack}>Offer Region</Text>
            <Icon
              name="chevron-up"
              size={moderateScale(20)}
              color={theme.colors.black}
            />
          </View>
          <View style={styles.regionDropdown}>
            {isLoadingRegions && (
              <Text style={styles.loadingText}>Loading regions...</Text>
            )}
            {!isLoadingRegions && regions.length === 0 && (
              <Text style={styles.errorText}>No regions available</Text>
            )}
            {!isLoadingRegions && regions.length > 0 && (
              <Dropdown
                style={styles.dropdown}
                placeholderStyle={styles.placeholderStyle}
                selectedTextStyle={styles.selectedTextStyle}
                inputSearchStyle={styles.inputSearchStyle}
                iconStyle={styles.iconStyle}
                data={regions.map(region => ({
                  label: region.name,
                  value: region.region_id,
                }))}
                search
                maxHeight={verticalScale(300)}
                labelField="label"
                valueField="value"
                placeholder="Select region"
                searchPlaceholder="Search..."
                value={selectedRegion}
                onChange={item => {
                  setSelectedRegion(item.value);
                }}
                renderLeftIcon={() => (
                  <Icon
                    name="map-pin"
                    size={moderateScale(20)}
                    color={theme.colors.grey1}
                  />
                )}
              />
            )}
          </View>
        </View>
        {/* Minimum Cashback */}
        <View style={styles.section}>
          <View style={styles.sectionHeaderRow}>
            <Text style={styles.sectionTitleBlack}>Minimum Cashback</Text>
            <Icon
              name="chevron-up"
              size={moderateScale(20)}
              color={theme.colors.black}
            />
          </View>
          <View style={styles.cashbackLabelRow}>
            <Text style={styles.cashbackLabel}>Cashback Range</Text>
            <Text style={styles.selectedRangeBlack}>
              ${cashbackRange[0]} - $
              {cashbackRange[1] === 300 ? '300+' : cashbackRange[1]}
            </Text>
          </View>
          {/* Bar chart and slider */}
          <View
            style={styles.barChartRow}
            onLayout={e => setBarChartWidth(e.nativeEvent.layout.width)}
          >
            {barChartWidth > 0 &&
              DUMMY_PRODUCT_COUNTS.map((count, idx) => {
                const barWidth =
                  (barChartWidth - 8 * (DUMMY_PRODUCT_COUNTS.length - 1)) /
                  DUMMY_PRODUCT_COUNTS.length; // 8px gap between bars
                const barHeight = 20 + (count / BAR_MAX_COUNT) * 60; // min 20, max 80

                return (
                  <View
                    key={idx}
                    style={[
                      styles.bar,
                      {
                        height: barHeight,
                        width: barWidth,
                        marginRight:
                          idx !== DUMMY_PRODUCT_COUNTS.length - 1
                            ? moderateScale(8)
                            : 0,
                      },
                      isBarInRange(idx) ? styles.barActive : styles.barInactive,
                    ]}
                  />
                );
              })}
          </View>
          <View style={styles.sliderRowCustom}>
            <View
              style={styles.flex1MarginH8}
              onLayout={e => setSliderWidth(e.nativeEvent.layout.width)}
            >
              {sliderWidth > 0 && (
                <MultiSlider
                  values={cashbackRange}
                  min={0}
                  max={300}
                  step={1}
                  allowOverlap={false}
                  snapped
                  onValuesChange={vals => setCashbackRange([vals[0], vals[1]])}
                  sliderLength={sliderWidth}
                  trackStyle={{
                    height: verticalScale(6),
                    borderRadius: moderateScale(3),
                    backgroundColor: theme.colors.border, // gray for the whole track
                  }}
                  selectedStyle={{
                    backgroundColor: theme.colors.primary, // yellow for the selected range
                    height: verticalScale(6),
                  }}
                  unselectedStyle={{
                    backgroundColor: theme.colors.border, // gray for unselected
                    height: verticalScale(6),
                  }}
                  customMarker={SliderMarker}
                />
              )}
              <View style={styles.sliderLabelRow}>
                <Text style={styles.rangeValueBlack}>$1</Text>
                <Text style={styles.rangeValueBlack}>$300+</Text>
              </View>
            </View>
          </View>
          {/* Checkboxes */}
          <View style={styles.checkboxRowCustom}>
            <CheckBox
              checked={cashbackChecks.under25}
              onPress={() => toggleCashback('under25')}
              containerStyle={styles.checkBoxContainer}
              checkedColor={theme.colors.primary}
              uncheckedColor={theme.colors.border}
              iconType="material"
              checkedIcon="check-box"
              uncheckedIcon="check-box-outline-blank"
            />
            <Text style={styles.checkboxLabelBlack}>
              Under $25 <Text style={styles.checkboxCount}>(10)</Text>
            </Text>
          </View>
          <View style={styles.checkboxRowCustom}>
            <CheckBox
              checked={cashbackChecks.between25_50}
              onPress={() => toggleCashback('between25_50')}
              containerStyle={styles.checkBoxContainer}
              checkedColor={theme.colors.primary}
              uncheckedColor={theme.colors.border}
              iconType="material"
              checkedIcon="check-box"
              uncheckedIcon="check-box-outline-blank"
            />
            <Text style={styles.checkboxLabelBlack}>
              $25 — $50 <Text style={styles.checkboxCount}>(27)</Text>
            </Text>
          </View>
          <View style={styles.checkboxRowCustom}>
            <CheckBox
              checked={cashbackChecks.between50_100}
              onPress={() => toggleCashback('between50_100')}
              containerStyle={styles.checkBoxContainer}
              checkedColor={theme.colors.primary}
              uncheckedColor={theme.colors.border}
              iconType="material"
              checkedIcon="check-box"
              uncheckedIcon="check-box-outline-blank"
            />
            <Text style={styles.checkboxLabelBlack}>
              $50 — $100 <Text style={styles.checkboxCount}>(23)</Text>
            </Text>
          </View>
          <TouchableOpacity>
            <Text style={styles.viewAllGold}>View All</Text>
          </TouchableOpacity>
        </View>
        {/* Store Type */}
        <View style={styles.section}>
          <View style={styles.sectionHeaderRow}>
            <Text style={styles.sectionTitleBlack}>Store Type</Text>
            <Icon
              name="chevron-up"
              size={moderateScale(20)}
              color={theme.colors.black}
            />
          </View>
          {productCategories.map(category => (
            <View key={category} style={styles.checkboxRowCustom}>
              <CheckBox
                checked={storeTypeChecks[category] || false}
                onPress={() => toggleStoreType(category)}
                containerStyle={styles.checkBoxContainer}
                checkedColor={theme.colors.primary}
                uncheckedColor={theme.colors.border}
                iconType="material"
                checkedIcon="check-box"
                uncheckedIcon="check-box-outline-blank"
              />
              <Text style={styles.checkboxLabelBlack}>
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </Text>
            </View>
          ))}
          <TouchableOpacity>
            <Text style={styles.viewAllUnderline}>View All</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
      {/* Apply Filter Button */}
      <TouchableOpacity style={styles.applyButton} onPress={onClose}>
        <Text style={styles.applyButtonText}>Apply Filter</Text>
      </TouchableOpacity>
    </View>
  );
};

const useStyles = makeStyles(theme => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    borderTopLeftRadius: moderateScale(20),
    borderTopRightRadius: moderateScale(20),
    paddingBottom: moderateScale(16),
  },
  headerContainer: {
    paddingTop: moderateScale(12),
    backgroundColor: theme.colors.background,
    borderTopLeftRadius: moderateScale(20),
    borderTopRightRadius: moderateScale(20),
  },
  dragIndicator: {
    alignSelf: 'center',
    width: moderateScale(80),
    height: verticalScale(6),
    borderRadius: moderateScale(4),
    backgroundColor: theme.colors.border,
    marginBottom: verticalScale(6),
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: moderateScale(20),
    paddingBottom: verticalScale(8),
    paddingTop: verticalScale(4),
  },
  headerTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerIcon: {
    marginRight: moderateScale(8),
  },
  headerTitle: {
    fontSize: moderateScale(22),
    fontWeight: '400',
    color: theme.colors.foreground,
  },
  resetBtn: {
    alignSelf: 'center',
  },
  resetText: {
    color: theme.colors.primary,
    fontWeight: '400',
    fontSize: moderateScale(18),
    textDecorationLine: 'underline',
  },
  headerDivider: {
    height: verticalScale(1),
    backgroundColor: theme.colors.border,
    marginTop: verticalScale(6),
  },
  scrollContent: {
    padding: moderateScale(16),
    paddingBottom: verticalScale(60),
  },
  section: {
    marginBottom: verticalScale(16),
    backgroundColor: theme.colors.background,
    borderRadius: moderateScale(12),
    paddingBottom: verticalScale(6),
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: verticalScale(8),
  },
  sectionTitle: {
    fontSize: moderateScale(15),
    fontWeight: '600',
  },
  chevron: {
    fontSize: moderateScale(16),
    color: theme.colors.grey1,
  },
  rangeLabel: {
    fontSize: moderateScale(13),
    color: theme.colors.grey1,
    marginBottom: verticalScale(4),
  },
  sliderRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: verticalScale(4),
  },
  sliderPlaceholder: {
    flex: 1,
    height: verticalScale(32),
    backgroundColor: theme.colors.primary,
    borderRadius: moderateScale(8),
    marginHorizontal: verticalScale(8),
  },
  rangeValue: {
    fontSize: moderateScale(12),
    color: theme.colors.grey1,
  },
  rangeValueRow: {
    alignItems: 'flex-end',
    marginBottom: verticalScale(8),
  },
  selectedRange: {
    fontSize: moderateScale(13),
    color: theme.colors.primary,
    fontWeight: 'bold',
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: verticalScale(4),
  },
  viewAll: {
    color: theme.colors.primary,
    fontSize: moderateScale(13),
    marginTop: verticalScale(4),
    marginBottom: verticalScale(4),
  },
  applyButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: moderateScale(24),
    margin: moderateScale(16),
    paddingVertical: verticalScale(12),
    alignItems: 'center',
    justifyContent: 'center',
  },
  applyButtonText: {
    color: theme.colors.background,
    fontWeight: 'bold',
    fontSize: moderateScale(16),
  },
  sectionHeaderRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: verticalScale(12),
  },
  sectionTitleBlack: {
    fontSize: moderateScale(18),
    fontWeight: '400',
    color: theme.colors.foreground,
  },
  regionDropdown: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: theme.colors.background,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: moderateScale(8),
    paddingHorizontal: moderateScale(16),
    paddingVertical: verticalScale(6),
    marginBottom: verticalScale(8),
  },

  regionDropdownText: {
    color: theme.colors.grey1,
    fontSize: moderateScale(20),
    fontWeight: '400',
  },
  cashbackLabelRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: verticalScale(8),
  },
  cashbackLabel: {
    fontSize: moderateScale(15),
    color: theme.colors.grey1,
    fontWeight: '400',
  },
  selectedRangeBlack: {
    fontSize: moderateScale(18),
    color: theme.colors.foreground,
    fontWeight: '400',
  },
  barChartRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'center',
    marginBottom: verticalScale(8),
    marginTop: verticalScale(8),
    height: verticalScale(100),
    marginHorizontal: moderateScale(16),
  },
  bar: {
    borderRadius: moderateScale(8),
    backgroundColor: theme.colors.primary,
  },
  barSmall: {
    height: verticalScale(34),
    backgroundColor: theme.colors.primary,
  },
  barMedium: {
    height: verticalScale(38),
    backgroundColor: theme.colors.primary,
  },
  barLarge: {
    height: verticalScale(60),
    backgroundColor: theme.colors.primary,
  },
  barFaded: {
    opacity: 0.3,
  },
  sliderRowCustom: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: verticalScale(8),
    marginTop: verticalScale(4),
    paddingHorizontal: moderateScale(8),
  },
  sliderTrack: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: moderateScale(8),
    position: 'relative',
    height: verticalScale(24),
  },
  sliderLine: {
    position: 'absolute',
    top: '50%',
    left: 0,
    right: 0,
    height: verticalScale(4),
    backgroundColor: theme.colors.border,
    borderRadius: moderateScale(2),
    zIndex: 0,
  },
  sliderThumb: {
    width: moderateScale(32),
    height: verticalScale(32),
    borderRadius: moderateScale(16),
    borderWidth: 3,
    borderColor: theme.colors.primary,
    backgroundColor: theme.colors.background,
    zIndex: 1,
  },
  rangeValueBlack: {
    fontSize: moderateScale(14),
    color: theme.colors.foreground,
    fontWeight: '400',
    width: moderateScale(44),
    textAlign: 'center',
  },
  checkboxRowCustom: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: verticalScale(6),
  },
  checkboxCustom: {
    width: moderateScale(24),
    height: verticalScale(24),
    borderRadius: moderateScale(6),
    borderWidth: 2,
    borderColor: theme.colors.border,
    backgroundColor: theme.colors.background,
    marginRight: moderateScale(12),
  },
  checkboxLabelBlack: {
    fontSize: moderateScale(18),
    color: theme.colors.foreground,
    fontWeight: '400',
  },
  checkboxCount: {
    color: theme.colors.grey1,
    fontWeight: '400',
    fontSize: moderateScale(16),
  },
  viewAllGold: {
    color: theme.colors.primary,
    fontSize: moderateScale(16),
    marginTop: verticalScale(4),
    marginBottom: verticalScale(4),
    fontWeight: '400',
  },
  viewAllUnderline: {
    color: theme.colors.primary,
    fontSize: moderateScale(16),
    marginTop: verticalScale(4),
    marginBottom: verticalScale(4),
    fontWeight: '400',
    textDecorationLine: 'underline',
  },
  barActive: {
    backgroundColor: theme.colors.primary,
    opacity: 1,
  },
  barInactive: {
    backgroundColor: theme.colors.border,
    opacity: 0.5,
  },
  flex1: { flex: 1 },
  flex1MarginH8: { flex: 1, marginHorizontal: verticalScale(8) },
  sliderLabelRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: verticalScale(2),
  },
  checkBoxContainer: { padding: 0, margin: 0 },
  sliderSelected: {
    backgroundColor: theme.colors.primary,
    height: verticalScale(6),
  },
  sliderUnselected: {
    backgroundColor: theme.colors.border,
    height: verticalScale(6),
  },
  loadingText: {
    color: theme.colors.grey1,
    fontSize: moderateScale(16),
    textAlign: 'center',
  },
  errorText: {
    color: theme.colors.grey1,
    fontSize: moderateScale(16),
    textAlign: 'center',
  },
  dropdown: {
    borderWidth: 0,
    backgroundColor: 'transparent',
    borderRadius: moderateScale(8),
    paddingHorizontal: moderateScale(16),
    paddingVertical: verticalScale(6),
    marginBottom: verticalScale(8),
    width: '100%',
  },
  placeholderStyle: {
    fontSize: moderateScale(16),
    color: theme.colors.grey1,
  },
  selectedTextStyle: {
    fontSize: moderateScale(16),
    color: theme.colors.foreground,
  },
  inputSearchStyle: {
    height: verticalScale(40),
    fontSize: moderateScale(16),
    color: theme.colors.foreground,
  },
  iconStyle: {
    width: moderateScale(20),
    height: verticalScale(20),
    tintColor: theme.colors.grey1,
  },
}));

export default FilterModal;
