import React from 'react';
import {
  Platform,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { SafeAreaView } from 'react-native-safe-area-context';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import { makeStyles, useTheme } from '@rneui/themed';

import { FilterIconOutline } from '@/constants/svgs';

interface SneakersHeaderProps {
  search: string;
  setSearch: (search: string) => void;
  onFilterPress?: () => void;
}

const SneakersHeader: React.FC<SneakersHeaderProps> = ({
  search,
  setSearch,
  onFilterPress,
}) => {
  const { theme } = useTheme();
  const styles = useStyles(theme);

  return (
    <SafeAreaView
      edges={['top', 'left', 'right']}
      style={styles.headerSafeArea}
    >
      <LinearGradient
        colors={[theme.colors.foreground, theme.colors.brownGradient]}
      >
        <View style={styles.headerContent}>
          <View style={styles.headerRow}>
            <Text style={styles.headerTitle}>Sneakers</Text>
            <TouchableOpacity
              style={styles.settingsBtn}
              onPress={onFilterPress}
            >
              <FilterIconOutline color={theme.colors.white} />
            </TouchableOpacity>
          </View>
          <View style={styles.searchBoxContainer}>
            <TextInput
              style={styles.searchBox}
              placeholder="Search by name or SKU"
              placeholderTextColor={theme.colors.grey2}
              value={search}
              onChangeText={setSearch}
            />
          </View>
        </View>
      </LinearGradient>
    </SafeAreaView>
  );
};

const useStyles = makeStyles(theme => ({
  headerSafeArea: {
    backgroundColor: theme.colors.foreground,
  },
  headerContent: {
    paddingBottom: verticalScale(50),
    paddingTop: Platform.OS === 'ios' ? verticalScale(5) : verticalScale(10),
    paddingHorizontal: moderateScale(16),
    gap: verticalScale(10),
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: verticalScale(12),
  },
  headerTitle: {
    color: theme.colors.white,
    fontSize: moderateScale(20),
    fontWeight: '500',
  },
  settingsBtn: {
    borderWidth: moderateScale(1),
    borderColor: theme.colors.border,
    borderRadius: moderateScale(100),
    padding: moderateScale(8),
  },
  searchBoxContainer: {
    marginTop: verticalScale(0),
    marginBottom: verticalScale(0),
  },
  searchBox: {
    width: '100%',
    height: moderateScale(40),
    borderRadius: moderateScale(20),
    backgroundColor: `${theme.colors.grey0}10`,
    color: theme.colors.white,
    paddingHorizontal: moderateScale(16),
    fontSize: moderateScale(14),
    borderWidth: 1,
    borderColor: `${theme.colors.white}40`,
  },
}));

export default SneakersHeader;
