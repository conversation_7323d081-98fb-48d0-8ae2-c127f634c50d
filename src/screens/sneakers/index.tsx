import React, { useEffect, useMemo, useRef, useState } from 'react';
import { FlatList, Text, View } from 'react-native';
import RBSheet from 'react-native-raw-bottom-sheet';
import { SafeAreaView } from 'react-native-safe-area-context';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import { useDispatch, useSelector } from 'react-redux';
import { makeStyles, useTheme } from '@rneui/themed';

import { SneakersSkeleton } from '@/components/skeleton';
import { AppDispatch, RootState } from '@/store';
import { fetchSneakers } from '@/store/slices/sneakersSlice';
import { Sneaker } from '@/types/sneakers';

import FilterModal from './components/FilterModal';
import { SneakerCard, SneakersHeader } from './components';

interface RBSheetRef {
  open: () => void;
  close: () => void;
}

const Sneakers: React.FC = () => {
  const { theme } = useTheme();
  const styles = useStyles(theme);
  const [search, setSearch] = useState('');
  const [_debouncedSearch, setDebouncedSearch] = useState('');
  const dispatch = useDispatch<AppDispatch>();
  const { sneakers, loading, error } = useSelector(
    (state: RootState) => state.sneakers,
  );
  const refRBSheet = useRef<RBSheetRef>(null);

  useEffect(() => {
    dispatch(fetchSneakers());
  }, [dispatch]);

  // Debounce search to prevent rapid re-renders
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(search);
    }, 300);

    return () => clearTimeout(timer);
  }, [search]);

  const gridData: (Sneaker | null)[] = useMemo(() => {
    const arr: (Sneaker | null)[] = [...sneakers];
    if (arr.length % 2 === 1) arr.push(null);

    return arr;
  }, [sneakers]);

  if (loading) {
    return <SneakersSkeleton />;
  }

  let content = null;
  if (error) {
    content = (
      <View style={styles.loaderContainer}>
        <Text style={styles.errorText}>Error: {error}</Text>
      </View>
    );
  } else {
    content = (
      <FlatList<Sneaker | null>
        data={gridData}
        keyExtractor={(item, idx) => (item ? item.product_id : `empty-${idx}`)}
        renderItem={({ item }) => <SneakerCard item={item} />}
        contentContainerStyle={styles.listContent}
        numColumns={2}
        columnWrapperStyle={styles.columnWrapper}
        showsVerticalScrollIndicator={false}
      />
    );
  }

  return (
    <SafeAreaView edges={['bottom']} style={styles.container}>
      <SneakersHeader
        search={search}
        setSearch={setSearch}
        onFilterPress={() => refRBSheet.current?.open()}
      />
      {content}
      <RBSheet
        ref={refRBSheet}
        height={verticalScale(500)}
        customStyles={{
          container: {
            borderTopLeftRadius: moderateScale(20),
            borderTopRightRadius: moderateScale(20),
          },
        }}
      >
        <FilterModal onClose={() => refRBSheet.current?.close()} />
      </RBSheet>
    </SafeAreaView>
  );
};

const useStyles = makeStyles(theme => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    color: theme.colors.error,
    fontSize: moderateScale(14),
    textAlign: 'center',
  },
  listContent: {
    padding: moderateScale(12),
  },
  columnWrapper: {
    justifyContent: 'space-between',
  },
}));

export default Sneakers;
