import React, { useState } from 'react';
import { SafeAreaView, View } from 'react-native';
import { moderateScale, scale, verticalScale } from 'react-native-size-matters';
import { ForgotPasswordIcon } from '@assets/images/svg';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Icon, Input, makeStyles, Text, useTheme } from '@rneui/themed';

import AppButton from '@/components/AppButton';
import { AUTH_ROUTES, AuthStackNavigatorParamList } from '@/types/routes';

type ForgotPasswordScreenNavigationProp = NativeStackNavigationProp<
  AuthStackNavigatorParamList,
  keyof AuthStackNavigatorParamList
>;

const ForgotPasswordScreen = ({
  navigation,
}: {
  navigation: ForgotPasswordScreenNavigationProp;
}) => {
  const { theme } = useTheme();
  const styles = useStyles(theme);

  const [email, setEmail] = useState('');

  const handleSendResetInstructions = () => {
    navigation.navigate(AUTH_ROUTES.RESET_PASSWORD);
  };

  const handleGoBack = () => {
    navigation.goBack();
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <View style={styles.headingContainer}>
          <ForgotPasswordIcon style={styles.icon} />
          <Text style={styles.heading}>Forgot password?</Text>
          <Text style={styles.subheading}>
            Enter your email address below and we'll send you password reset
            instructions.
          </Text>
        </View>
        <View style={styles.formContainer}>
          <View style={styles.form}>
            <Text style={styles.label}>Email</Text>
            <Input
              placeholder="<EMAIL>"
              value={email}
              onChangeText={setEmail}
              autoCapitalize="none"
              keyboardType="email-address"
            />
          </View>
          <View style={styles.buttonContainer}>
            <View style={styles.buttonColumn}>
              <AppButton
                type="primary"
                size="large"
                onPress={handleSendResetInstructions}
                containerStyle={styles.loginButtonContainer}
              >
                <Text style={styles.loginButtonText}>
                  Send Reset Instructions
                </Text>
              </AppButton>
              <AppButton
                type="ghost"
                size="large"
                onPress={handleGoBack}
                containerStyle={styles.loginButtonContainer}
              >
                <Icon
                  name="arrowleft"
                  type="antdesign"
                  style={styles.arrowLeft}
                />
                <Text style={styles.loginButtonText}>Go Back</Text>
              </AppButton>
            </View>
            <View>
              <Text style={styles.termsText}>
                If you don't see your reset email be sure to check your spam
                filter for an email from{' '}
                <Text style={styles.supportLink}><EMAIL></Text>
              </Text>
            </View>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

const useStyles = makeStyles(theme => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  content: {
    flex: 1,
    paddingHorizontal: scale(24),
    alignItems: 'center',
    marginTop: verticalScale(18),
  },
  backButton: {
    position: 'absolute',
    top: verticalScale(6),
    left: scale(16),
    width: scale(40),
    height: scale(40),
    borderRadius: scale(20),
    backgroundColor:
      theme.mode === 'dark' ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.02)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  icon: {
    marginHorizontal: 'auto',
    marginVertical: verticalScale(20),
  },
  headingContainer: {
    width: '100%',
  },
  heading: {
    fontSize: moderateScale(34),
    fontWeight: '600',
    color: theme.colors.foreground,
  },
  subheading: {
    fontSize: moderateScale(16),
    color: theme.colors.neutralGrey,
  },
  form: {
    width: '100%',
    marginTop: verticalScale(20),
  },
  label: {
    fontSize: moderateScale(16),
    fontWeight: '500',
    color: theme.colors.foreground,
    marginBottom: verticalScale(8),
  },
  rememberForgotContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: verticalScale(24),
  },
  checkboxContainer: {
    backgroundColor: 'transparent',
    borderWidth: 0,
    padding: 0,
    margin: 0,
  },
  checkboxText: {
    fontSize: moderateScale(14),
    fontWeight: 'normal',
    color: theme.colors.foreground,
    marginLeft: scale(8),
  },
  forgotPassword: {
    fontSize: moderateScale(14),
    color: theme.colors.primary,
    textDecorationLine: 'underline',
  },
  loginButtonContainer: {
    width: '100%',
  },
  loginButtonText: {
    fontSize: moderateScale(16),
    fontWeight: '600',
    color: theme.colors.secondary,
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    marginBottom: verticalScale(24),
  },
  divider: {
    flex: 1,
    height: 1,
    backgroundColor: theme.colors.grey5,
  },
  dividerText: {
    paddingHorizontal: scale(16),
    color: theme.colors.neutralGrey,
    fontSize: moderateScale(14),
  },
  socialButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
    marginBottom: verticalScale(40),
  },
  socialButton: {
    width: scale(80),
    marginHorizontal: scale(8),
  },
  registerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  registerText: {
    fontSize: moderateScale(14),
    marginRight: scale(4),
  },
  registerLink: {
    fontSize: moderateScale(14),
    color: theme.colors.primary,
    fontWeight: '500',
    textDecorationLine: 'underline',
  },
  termsText: {
    fontSize: moderateScale(12),
    textAlign: 'center',
    color: theme.colors.neutralGrey,
    lineHeight: moderateScale(18),
  },
  supportLink: {
    fontWeight: '500',
  },
  arrowLeft: {
    marginRight: scale(4),
  },
  buttonContainer: {
    width: '100%',
    marginBottom: verticalScale(24),
  },
  buttonColumn: {
    gap: verticalScale(12),
    marginBottom: verticalScale(24),
  },
  formContainer: {
    width: '100%',
    flex: 1,
    justifyContent: 'space-between',
  },
}));

export default ForgotPasswordScreen;
