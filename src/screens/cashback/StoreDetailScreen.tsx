import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Image,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';
import LinearGradient from 'react-native-linear-gradient';
import { SafeAreaView } from 'react-native-safe-area-context';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/Feather';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation, useRoute } from '@react-navigation/native';
import { makeStyles, useTheme } from '@rneui/themed';

import { CouponIcon } from '@/constants/svgs';
import { AppDispatch, RootState } from '@/store';
import { fetchStoreDetail } from '@/store/slices/cashbackSlice';
import { StoreRegion } from '@/types/cashback';

const COUNTRY_MAP: Record<string, string> = {
  EN: 'United Kingdom',
  UK: 'United Kingdom',
  DE: 'Germany',
  PL: 'Poland',
  ES: 'Spain',
  FR: 'France',
  IT: 'Italy',
  NL: 'Netherlands',
  BE: 'Belgium',
  SK: 'Slovakia',
  CZ: 'Czech Republic',
  // Add more mappings as needed
};

const StoreDetailScreen = () => {
  const { theme } = useTheme();
  const styles = useStyles(theme);
  const navigation = useNavigation();
  const route = useRoute();
  const dispatch = useDispatch<AppDispatch>();
  const { storeDetail, loading, error } = useSelector(
    (state: RootState) => state.cashback,
  );
  const [selectedRegion, setSelectedRegion] = useState<StoreRegion | null>(
    null,
  );

  const storeId = (route.params as { storeId: string })?.storeId;

  useEffect(() => {
    if (storeId) {
      dispatch(fetchStoreDetail(storeId));
    }
  }, [dispatch, storeId]);

  useEffect(() => {
    if (storeDetail?.store_regions && storeDetail.store_regions.length > 0) {
      setSelectedRegion(storeDetail.store_regions[0]);
    }
  }, [storeDetail]);

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{error}</Text>
      </View>
    );
  }

  if (!storeDetail) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Store not found</Text>
      </View>
    );
  }

  const getCountryName = (region: string) => {
    return COUNTRY_MAP[region] || region;
  };

  return (
    <View style={styles.container}>
      <SafeAreaView
        edges={['top', 'left', 'right']}
        style={styles.headerSafeArea}
      >
        <LinearGradient
          colors={[theme.colors.foreground, theme.colors.brownGradient]}
        >
          <View style={styles.headerContent}>
            <View style={styles.headerRow}>
              <TouchableOpacity
                style={styles.backButton}
                onPress={() => navigation.goBack()}
              >
                <Icon name="arrow-left" size={24} color={theme.colors.white} />
              </TouchableOpacity>

              <View style={styles.placeholder} />
            </View>
          </View>
        </LinearGradient>
      </SafeAreaView>

      <View style={styles.storeSection}>
        <View style={styles.logoContainer}>
          <View style={styles.logo}>
            <Image
              source={{ uri: storeDetail.logo }}
              style={styles.logoImage}
            />
          </View>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Store Name */}
        <View style={styles.storeInfo}>
          <Text style={styles.storeName}>{storeDetail.name}</Text>
        </View>

        {/* Offers Available */}
        <View style={styles.offersContainer}>
          <CouponIcon color={theme.colors.cashbackText} />
          <Text style={styles.offersText}>24 Offers Available</Text>
        </View>

        {/* Region Selector */}
        {storeDetail.store_regions && storeDetail.store_regions.length > 0 && (
          <View style={styles.regionDropdownContainer}>
            <Dropdown
              style={styles.regionDropdown}
              placeholderStyle={styles.regionPlaceholderStyle}
              selectedTextStyle={styles.regionSelectedTextStyle}
              iconStyle={styles.regionIconStyle}
              data={storeDetail.store_regions.map((region: StoreRegion) => ({
                label: getCountryName(region.region),
                value: region.id,
                region: region,
              }))}
              maxHeight={verticalScale(200)}
              labelField="label"
              valueField="value"
              placeholder="Select region"
              value={selectedRegion?.id}
              onChange={item => {
                setSelectedRegion(item.region);
              }}
              renderLeftIcon={() => {
                const getFlagEmoji = (region: string) => {
                  switch (region) {
                    case 'PL':
                      return '🇵🇱';
                    case 'DE':
                      return '🇩🇪';
                    case 'UK':
                    case 'EN':
                      return '🇬🇧';
                    default:
                      return '🏳️';
                  }
                };

                return (
                  <View style={styles.flagContainer}>
                    <Text style={styles.flagEmoji}>
                      {getFlagEmoji(selectedRegion?.region || '')}
                    </Text>
                  </View>
                );
              }}
            />
          </View>
        )}

        {/* Cashback Banner */}
        {storeDetail.special_deal && (
          <View style={styles.cashbackBanner}>
            <View style={styles.cashbackContent}>
              <Text style={styles.cashbackTitle}>Cashback Offer</Text>
              <View style={styles.cashbackAmount}>
                <Icon name="star" size={16} color={theme.colors.primary} />
                <Text style={styles.cashbackPercent}>
                  Upto {storeDetail.special_deal_percent}%
                </Text>
                <Icon
                  name="chevron-right"
                  size={16}
                  color={theme.colors.primary}
                />
              </View>
            </View>
          </View>
        )}

        {/* About Shop */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>About Shop</Text>
            <Icon name="chevron-up" size={16} color={theme.colors.grey1} />
          </View>
          <Text style={styles.sectionContent}>{storeDetail.description}</Text>
        </View>

        {/* Promotions */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Promotions (15)</Text>
            <View style={styles.badge}>
              <Text style={styles.badgeText}>5 New</Text>
            </View>
            <Icon name="chevron-down" size={16} color={theme.colors.grey1} />
          </View>
        </View>

        {/* Coupons */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Coupons (4)</Text>
            <View style={styles.badgeWarning}>
              <Text style={styles.badgeText}>2 Expiring Soon</Text>
            </View>
            <Icon name="chevron-down" size={16} color={theme.colors.grey1} />
          </View>
        </View>

        {/* Confirmation Info */}
        {selectedRegion && (
          <View style={styles.confirmationInfo}>
            <Text style={styles.confirmationText}>
              Confirmation Period: {selectedRegion.confirmation_min_days}-
              {selectedRegion.confirmation_max_days} days
            </Text>
            {selectedRegion.auto_activate && (
              <Text style={styles.autoActivateText}>
                Auto-activation available
              </Text>
            )}
          </View>
        )}
      </ScrollView>

      {/* Activate Button */}
      <View style={styles.bottomContainer}>
        <TouchableOpacity style={styles.activateButton}>
          <Icon name="check" size={20} color={theme.colors.white} />
          <Text style={styles.activateButtonText}>Activate Cashback</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const useStyles = makeStyles(theme => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: moderateScale(20),
  },
  errorText: {
    color: theme.colors.error,
    fontSize: moderateScale(16),
  },
  headerSafeArea: {
    backgroundColor: theme.colors.foreground,
  },
  headerContent: {
    paddingBottom: verticalScale(30),
    paddingTop: verticalScale(10),
    paddingHorizontal: moderateScale(16),
    gap: verticalScale(10),
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: verticalScale(12),
  },
  headerTitle: {
    color: theme.colors.white,
    fontSize: moderateScale(20),
    fontWeight: '500',
  },
  backButton: {
    borderWidth: moderateScale(1),
    borderColor: `${theme.colors.white}80`,
    borderRadius: moderateScale(100),
    padding: moderateScale(8),
  },
  placeholder: {
    width: moderateScale(40),
  },
  storeSection: {
    alignItems: 'center',
    marginTop: verticalScale(-30),
    marginBottom: verticalScale(10),
  },
  logoContainer: {
    marginBottom: verticalScale(20),
  },
  logo: {
    width: moderateScale(120),
    height: moderateScale(120),
    borderRadius: moderateScale(60),
    backgroundColor: theme.colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    shadowColor: theme.colors.black,
    shadowOffset: {
      width: 0,
      height: verticalScale(4),
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
    zIndex: 10,
  },
  logoImage: {
    width: '100%',
    height: '100%',
    borderRadius: moderateScale(60),
  },
  content: {
    flex: 1,
    paddingHorizontal: moderateScale(20),
  },
  storeInfo: {
    alignItems: 'center',
    paddingHorizontal: moderateScale(20),
  },
  storeName: {
    fontSize: moderateScale(24),
    fontWeight: '500',
    color: theme.colors.foreground,
    marginBottom: verticalScale(8),
    textAlign: 'center',
  },
  offersContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: verticalScale(15),
    justifyContent: 'center',
  },
  offersText: {
    marginLeft: moderateScale(8),
    fontSize: moderateScale(14),
    color: theme.colors.cashbackText,
    fontWeight: '500',
  },
  regionDropdownContainer: {
    marginBottom: verticalScale(20),
  },
  regionDropdown: {
    borderWidth: moderateScale(1),
    borderColor: theme.colors.border,
    borderRadius: moderateScale(30),
    paddingHorizontal: moderateScale(15),
    paddingVertical: verticalScale(12),
    backgroundColor: theme.colors.white,
    width: '50%',
    alignSelf: 'center',
  },
  regionPlaceholderStyle: {
    fontSize: moderateScale(16),
    color: theme.colors.grey1,
  },
  regionSelectedTextStyle: {
    fontSize: moderateScale(16),
    color: theme.colors.foreground,
    fontWeight: '500',
  },
  regionIconStyle: {
    width: moderateScale(20),
    height: verticalScale(20),
    color: theme.colors.grey1,
  },
  flagContainer: {
    marginRight: moderateScale(8),
  },
  flagEmoji: {
    fontSize: moderateScale(16),
  },
  cashbackBanner: {
    backgroundColor: theme.colors.foreground,
    borderRadius: moderateScale(12),
    padding: moderateScale(20),
    marginBottom: verticalScale(20),
  },
  cashbackContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  cashbackTitle: {
    color: theme.colors.white,
    fontSize: moderateScale(16),
    fontWeight: '600',
  },
  cashbackAmount: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  cashbackPercent: {
    color: theme.colors.primary,
    fontSize: moderateScale(16),
    fontWeight: '600',
    marginHorizontal: moderateScale(8),
  },
  section: {
    backgroundColor: theme.colors.white,
    borderRadius: moderateScale(12),
    padding: moderateScale(20),
    marginBottom: verticalScale(15),
    shadowColor: theme.colors.black,
    shadowOffset: {
      width: 0,
      height: verticalScale(2),
    },
    shadowOpacity: 0.1,
    shadowRadius: moderateScale(3.84),
    elevation: 5,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  sectionTitle: {
    fontSize: moderateScale(16),
    fontWeight: '600',
    color: theme.colors.foreground,
    flex: 1,
  },

  sectionContent: {
    fontSize: moderateScale(14),
    color: theme.colors.grey1,
    lineHeight: verticalScale(20),
    marginTop: verticalScale(10),
  },
  accordionContent: {
    marginTop: verticalScale(10),
  },
  badge: {
    backgroundColor: theme.colors.cashbackText,
    paddingHorizontal: moderateScale(8),
    paddingVertical: verticalScale(4),
    borderRadius: moderateScale(12),
    marginRight: moderateScale(10),
  },
  badgeWarning: {
    backgroundColor: theme.colors.warning,
    paddingHorizontal: moderateScale(8),
    paddingVertical: verticalScale(4),
    borderRadius: moderateScale(12),
    marginRight: moderateScale(10),
  },
  badgeText: {
    color: theme.colors.white,
    fontSize: moderateScale(12),
    fontWeight: '500',
  },
  confirmationInfo: {
    backgroundColor: theme.colors.cashbackBg,
    padding: verticalScale(15),
    borderRadius: moderateScale(8),
    marginBottom: verticalScale(20),
  },
  confirmationText: {
    fontSize: moderateScale(14),
    color: theme.colors.grey1,
    marginBottom: verticalScale(5),
  },
  autoActivateText: {
    fontSize: moderateScale(14),
    color: theme.colors.cashbackText,
    fontWeight: '500',
  },
  bottomContainer: {
    paddingHorizontal: moderateScale(20),
    paddingVertical: verticalScale(15),
    marginBottom: verticalScale(10),
    backgroundColor: theme.colors.white,
    borderTopWidth: moderateScale(1),
    borderTopColor: theme.colors.border,
  },
  activateButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: moderateScale(25),
    paddingVertical: verticalScale(12),
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  activateButtonText: {
    color: theme.colors.white,
    fontSize: moderateScale(16),
    fontWeight: '600',
    marginLeft: moderateScale(8),
  },
  accordionHeader: {
    paddingHorizontal: moderateScale(15),
  },
  accordionContainer: {
    backgroundColor: theme.colors.white,
    borderRadius: moderateScale(12),
    shadowColor: theme.colors.black,
    shadowOffset: {
      width: 0,
      height: verticalScale(2),
    },
    shadowOpacity: 0.1,
    shadowRadius: moderateScale(3.84),
    elevation: 5,
  },
  accordionItemContainer: {
    backgroundColor: theme.colors.white,
    borderRadius: moderateScale(12),
    shadowColor: theme.colors.black,
    shadowOffset: {
      width: 0,
      height: verticalScale(2),
    },
    shadowOpacity: 0.1,
    shadowRadius: moderateScale(3.84),
    elevation: 5,
  },
}));

export default StoreDetailScreen;
