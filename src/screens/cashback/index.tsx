import React, { useEffect } from 'react';
import { FlatList, View } from 'react-native';
import RBSheet from 'react-native-raw-bottom-sheet';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import { useDispatch } from 'react-redux';
import { makeStyles, useTheme } from '@rneui/themed';

import { AppDispatch } from '@/store';
import {
  fetchCashbackHomeData,
  fetchCashbackStores,
} from '@/store/slices/cashbackSlice';

import CashbackBanner from './components/CashbackBanner';
import CashbackFilterModal from './components/CashbackFilterModal';
import CashbackHeader from './components/CashbackHeader';
import CashbackShops from './components/CashbackShops';
import { PopularOffers, PopularStores } from './components';

const sections = [
  'HEADER',
  'BANNER',
  'POPULAR_SHOPS',
  'POPULAR_STORES',
  'POPULAR_OFFERS',
];

const CashbackScreen: React.FC = () => {
  const { theme } = useTheme();
  const styles = useStyles(theme);
  const [search, setSearch] = React.useState('');
  const dispatch = useDispatch<AppDispatch>();

  interface RBSheetRef {
    open: () => void;
    close: () => void;
  }
  const refRBSheet = React.useRef<RBSheetRef>(null);

  useEffect(() => {
    dispatch(fetchCashbackStores());
    dispatch(fetchCashbackHomeData());
  }, [dispatch]);

  // Log cashback screen data
  useEffect(() => {
    console.log('=== CASHBACK SCREEN DATA ===');
    console.log('Search:', search);
    console.log('Sections:', sections);
    console.log('===========================');
  }, [search]);

  const renderSection = ({ item }: { item: string }) => {
    switch (item) {
      case 'BANNER':
        return <CashbackBanner />;
      case 'POPULAR_SHOPS':
        return <CashbackShops />;
      case 'POPULAR_STORES':
        return <PopularStores />;
      case 'POPULAR_OFFERS':
        return <PopularOffers />;
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      <FlatList
        data={sections}
        keyExtractor={i => i}
        renderItem={renderSection}
        showsVerticalScrollIndicator={false}
        ListHeaderComponent={
          <CashbackHeader
            search={search}
            setSearch={setSearch}
            onFilterPress={() => refRBSheet.current?.open()}
          />
        }
        bounces={false}
      />
      <RBSheet
        ref={refRBSheet}
        height={verticalScale(450)}
        customStyles={{
          container: {
            borderTopLeftRadius: moderateScale(20),
            borderTopRightRadius: moderateScale(20),
          },
        }}
      >
        <CashbackFilterModal onSelect={() => refRBSheet.current?.close()} />
      </RBSheet>
    </View>
  );
};

const useStyles = makeStyles(theme => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
}));

export default CashbackScreen;
