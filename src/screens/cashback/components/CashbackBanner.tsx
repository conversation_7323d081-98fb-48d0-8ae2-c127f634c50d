import React from 'react';
import { Image, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Swiper from 'react-native-swiper';
import { makeStyles, useTheme } from '@rneui/themed';

const bannerImages = [
  require('../../../../assets/images/png/banner1.png'),
  require('../../../../assets/images/png/banner2.png'),
];

const CashbackBanner = () => {
  const { theme } = useTheme();
  const styles = useStyles(theme);

  return (
    <View style={styles.bannerContainer}>
      <Swiper
        style={styles.swiper}
        showsPagination={true}
        dotColor={theme.colors.border}
        activeDotColor={theme.colors.primary}
        height={156}
        autoplay
        horizontal={true}
      >
        {bannerImages.map((img, index) => (
          <View key={index} style={styles.bannerSlide}>
            <Image source={img} style={styles.bannerImage} />
          </View>
        ))}
      </Swiper>
    </View>
  );
};

const useStyles = makeStyles(theme => ({
  bannerContainer: {
    marginTop: verticalScale(-12),
    marginHorizontal: moderateScale(16),
    shadowColor: theme.colors.greenShadow,
    shadowOffset: { width: 0, height: verticalScale(9) },
    shadowOpacity: 0.14,
    shadowRadius: moderateScale(23),
    elevation: 4,
    // overflow: 'hidden',
    zIndex: 1000,
    // backgroundColor: theme.colors.white,
  },
  swiper: {
    height: verticalScale(150),
  },
  bannerSlide: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: moderateScale(12),
    // backgroundColor: theme.colors.white,
  },
  bannerImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
    borderRadius: moderateScale(12),
  },
}));

export default CashbackBanner;
