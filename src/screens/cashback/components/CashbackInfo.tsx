import React from 'react';
import { Text, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import { makeStyles, useTheme } from '@rneui/themed';

const CashbackInfo = () => {
  const { theme } = useTheme();
  const styles = useStyles(theme);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Cashback</Text>
      <Text style={styles.description}>
        Welcome to the Cashback screen! Here you can view your cashback offers
        and rewards.
      </Text>
    </View>
  );
};

const useStyles = makeStyles(theme => ({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: moderateScale(16),
    backgroundColor: theme.colors.background,
  },
  title: {
    fontSize: moderateScale(24),
    fontWeight: 'bold',
    marginBottom: verticalScale(12),
    color: theme.colors.foreground,
  },
  description: {
    fontSize: moderateScale(16),
    color: theme.colors.neutralGrey,
    textAlign: 'center',
  },
}));

export default CashbackInfo;
