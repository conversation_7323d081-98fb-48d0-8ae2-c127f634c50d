import React from 'react';
import { Platform, Text, TouchableOpacity, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { SafeAreaView } from 'react-native-safe-area-context';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import { makeStyles, SearchBar, useTheme } from '@rneui/themed';

import { FilterIconOutline } from '@/constants/svgs';

interface CashbackHeaderProps {
  search: string;
  setSearch: (search: string) => void;
  onFilterPress?: () => void;
}

const CashbackHeader: React.FC<CashbackHeaderProps> = ({
  search,
  setSearch,
  onFilterPress,
}) => {
  const { theme } = useTheme();
  const styles = useStyles(theme);

  return (
    <SafeAreaView
      edges={['top', 'left', 'right']}
      style={styles.headerSafeArea}
    >
      <LinearGradient
        colors={[theme.colors.foreground, theme.colors.brownGradient]}
      >
        <View style={styles.headerContent}>
          <View style={styles.headerRow}>
            <Text style={styles.headerTitle}>Cashback</Text>
            {onFilterPress && (
              <TouchableOpacity
                style={styles.settingsBtn}
                onPress={onFilterPress}
              >
                <FilterIconOutline color={theme.colors.white} />
              </TouchableOpacity>
            )}
          </View>
          <View style={styles.searchBoxContainer}>
            <SearchBar
              placeholder="Search cashback offers"
              onChangeText={setSearch}
              value={search}
              platform="default"
              containerStyle={{
                backgroundColor: 'transparent',
                padding: 0,
                borderTopWidth: 0,
                borderBottomWidth: 0,
              }}
              inputContainerStyle={{
                backgroundColor: 'rgba(255,255,255,0.15)',
                borderRadius: moderateScale(20),
                height: moderateScale(40),
                borderWidth: 1,
                borderColor: `${theme.colors.white}40`,
              }}
              inputStyle={{
                color: theme.colors.white,
                fontSize: moderateScale(14),
              }}
              placeholderTextColor={theme.colors.grey2}
              round
              searchIcon={{ size: 32 }}
            />
          </View>
        </View>
      </LinearGradient>
    </SafeAreaView>
  );
};

const useStyles = makeStyles(theme => ({
  headerSafeArea: {
    backgroundColor: theme.colors.foreground,
  },
  headerContent: {
    paddingBottom: verticalScale(50),
    paddingTop: Platform.OS === 'ios' ? verticalScale(5) : verticalScale(10),
    paddingHorizontal: moderateScale(16),
    gap: verticalScale(10),
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: verticalScale(12),
  },
  headerTitle: {
    color: theme.colors.white,
    fontSize: moderateScale(20),
    fontWeight: '500',
  },
  searchBoxContainer: {
    marginTop: verticalScale(0),
    marginBottom: verticalScale(0),
  },
  settingsBtn: {
    borderWidth: moderateScale(1),
    borderColor: 'rgba(255,255,255,0.5)',
    borderRadius: moderateScale(100),
    padding: moderateScale(8),
  },
}));

export default CashbackHeader;
