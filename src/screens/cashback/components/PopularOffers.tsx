import React, { JSX } from 'react';
import { Image, Pressable, Text, TouchableOpacity, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import ArrowRight from 'react-native-vector-icons/SimpleLineIcons';
import { useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { makeStyles, useTheme } from '@rneui/themed';

import { RootState } from '@/store';
import { APP_ROUTES, AppStackNavigatorParamList } from '@/types/routes';

const PopularOffers = () => {
  const { theme } = useTheme();
  const styles = useStyles(theme);
  const navigation =
    useNavigation<NativeStackNavigationProp<AppStackNavigatorParamList>>();
  const { homeData } = useSelector((state: RootState) => state.cashback);

  if (!homeData?.promoted_offers) return null;

  const handleViewAll = () => {
    navigation.navigate(APP_ROUTES.ALL_OFFERS);
  };

  const rows: JSX.Element[] = [];
  for (let i = 0; i < homeData.promoted_offers.length; i += 2) {
    const chunk = homeData.promoted_offers.slice(i, i + 2);
    rows.push(
      <View key={i} style={styles.offerRow}>
        {chunk.map(offer => (
          <Pressable
            key={offer.id}
            style={styles.offerCard}
            onPress={() =>
              navigation.navigate(APP_ROUTES.DEAL_DETAIL, {
                deal: offer as import('@/types/deals').PromotedOffer,
              })
            }
          >
            <Image
              source={{ uri: offer.store.logo }}
              style={styles.offerImage}
            />
            <Text style={styles.offerBrand}>{offer.store.name}</Text>
            <Text style={styles.offerType}>Shoe Company</Text>
            <View style={styles.offerBadge}>
              <Text style={styles.offerBadgeText}>
                Upto {offer.voucher.promotion_percent}% Cashback
              </Text>
            </View>
          </Pressable>
        ))}
      </View>,
    );
  }

  return (
    <View>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Popular Offers</Text>
        <TouchableOpacity onPress={handleViewAll}>
          <Text style={styles.viewAll}>
            View All{' '}
            <ArrowRight
              name="arrow-right"
              size={moderateScale(9)}
              color={theme.colors.primary}
            />
          </Text>
        </TouchableOpacity>
      </View>
      <View style={styles.offerRowsContainer}>{rows}</View>
    </View>
  );
};

const useStyles = makeStyles(theme => ({
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: verticalScale(10),
    marginBottom: verticalScale(10),
    paddingHorizontal: moderateScale(16),
  },
  sectionTitle: {
    fontFamily: 'Inter',
    fontWeight: '500',
    fontSize: moderateScale(18),
    lineHeight: moderateScale(23),
    letterSpacing: -0.18,
    color: theme.colors.foreground,
  },
  viewAll: {
    fontFamily: 'Inter',
    fontWeight: '500',
    fontSize: moderateScale(12),
    lineHeight: moderateScale(19),
    letterSpacing: -0.12,
    color: theme.colors.primary,
  },
  offerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: verticalScale(8),
  },
  offerCard: {
    backgroundColor: theme.colors.white,
    borderRadius: moderateScale(8),
    alignItems: 'center',
    marginBottom: verticalScale(4),
    paddingVertical: verticalScale(18),
    paddingHorizontal: moderateScale(8),
    elevation: 2,
    shadowColor: theme.colors.black,
    shadowOffset: { width: 0, height: verticalScale(2) },
    shadowOpacity: 0.08,
    shadowRadius: moderateScale(8),
    width: '48%',
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  offerImage: {
    width: '90%',
    height: verticalScale(45),
    resizeMode: 'contain',
  },
  offerBrand: {
    marginTop: verticalScale(6),
    fontWeight: '600',
    fontSize: moderateScale(16),
    color: theme.colors.black,
    fontFamily: 'Inter',
  },
  offerType: {
    color: theme.colors.neutralGrey,
    fontSize: moderateScale(12),
    fontFamily: 'Inter',
    fontWeight: '500',
    marginBottom: verticalScale(6),
  },
  offerBadge: {
    backgroundColor: theme.colors.white,
    borderWidth: 1,
    borderColor: theme.colors.cashbackBorder,
    borderRadius: moderateScale(5),
    paddingHorizontal: moderateScale(10),
    paddingVertical: verticalScale(4),
    marginTop: verticalScale(8),
  },
  offerBadgeText: {
    color: theme.colors.cashbackText,
    fontWeight: '500',
    fontSize: moderateScale(13),
    textAlign: 'center',
    fontFamily: 'Inter',
  },
  offerRowsContainer: {
    paddingHorizontal: moderateScale(16),
  },
}));

export default PopularOffers;
