import React, { useEffect } from 'react';
import {
  ActivityIndicator,
  FlatList,
  Image,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/Feather';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { makeStyles, useTheme } from '@rneui/themed';

import { AppDispatch, RootState } from '@/store';
import { fetchCashbackStores } from '@/store/slices/cashbackSlice';
import { Store } from '@/types/cashback';
import { APP_ROUTES, AppStackNavigatorParamList } from '@/types/routes';

const PopularStores = () => {
  const { theme } = useTheme();
  const styles = useStyles(theme);
  const navigation =
    useNavigation<NativeStackNavigationProp<AppStackNavigatorParamList>>();
  const dispatch = useDispatch<AppDispatch>();
  const { stores, loading, error } = useSelector(
    (state: RootState) => state.cashback,
  );

  useEffect(() => {
    dispatch(fetchCashbackStores({}));
  }, [dispatch]);

  if (loading) {
    return <ActivityIndicator style={styles.loadingSpinner} />;
  }
  if (error) {
    return <Text style={styles.errorText}>{error}</Text>;
  }

  const renderItem = ({ item }: { item: Store }) => (
    <TouchableOpacity
      style={styles.card}
      activeOpacity={0.9}
      onPress={() =>
        navigation.navigate(APP_ROUTES.STORE_DETAIL, { storeId: item.id })
      }
    >
      <View style={styles.row}>
        <Image source={{ uri: item.logo }} style={styles.logo} />
        <View style={styles.info}>
          <Text style={styles.name}>{item.name}</Text>
          <Text style={styles.desc} numberOfLines={2}>
            {item.description}
          </Text>
          {!item.special_deal && (
            <View style={styles.discountBadge}>
              <Text style={styles.discountText}>
                {item.special_deal_percent
                  ? `• ${item.special_deal_percent}% In Store Discount`
                  : '10% In store discount'}
              </Text>
            </View>
          )}
        </View>
        <Icon
          name="chevron-right"
          size={moderateScale(20)}
          color={theme.colors.foreground}
          style={styles.arrowIcon}
        />
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.sectionContainer}>
      <View style={styles.headerRow}>
        <Text style={styles.header}>Popular Stores</Text>
        <TouchableOpacity
          onPress={() =>
            navigation.navigate(APP_ROUTES.ALL_STORES, { featured: false })
          }
        >
          <Text style={styles.viewAll}>View All {'>'}</Text>
        </TouchableOpacity>
      </View>
      <FlatList
        data={stores.slice(0, 3)}
        renderItem={renderItem}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const useStyles = makeStyles(theme => ({
  sectionContainer: {
    borderRadius: moderateScale(16),
    marginHorizontal: 0,
    marginTop: 0,
    marginBottom: verticalScale(16),
    paddingVertical: verticalScale(8),
    paddingHorizontal: 0,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: moderateScale(20),
    paddingTop: verticalScale(16),
    paddingBottom: verticalScale(8),
  },
  header: {
    fontFamily: 'Inter',
    fontWeight: '500',
    fontSize: moderateScale(18),
    lineHeight: moderateScale(23),
    letterSpacing: -0.18, // -1% of 18px
    color: theme.colors.foreground,
  },
  viewAll: {
    fontFamily: 'Inter',
    fontWeight: '500',
    fontSize: moderateScale(12),
    lineHeight: moderateScale(19),
    letterSpacing: -0.12, // -1% of 12px
    color: theme.colors.primary,
  },
  card: {
    backgroundColor: theme.colors.background,
    borderRadius: moderateScale(4),
    marginHorizontal: moderateScale(16),
    marginVertical: verticalScale(8),
    padding: moderateScale(18),
    boxShadow: `0px 11.82px 22.61px 0px ${theme.colors.grey3}`,
    elevation: 2,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  logo: {
    width: moderateScale(44),
    height: verticalScale(44),
    borderRadius: moderateScale(8),
    marginRight: moderateScale(14),
    backgroundColor: theme.colors.neutralGrey,
    marginTop: verticalScale(2),
  },
  info: {
    flex: 1,
    flexDirection: 'column',
  },
  name: {
    fontWeight: '700',
    fontSize: moderateScale(16),
    color: theme.colors.foreground,
    marginBottom: verticalScale(2),
  },
  desc: {
    fontSize: moderateScale(13),
    color: theme.colors.neutralGrey,
    marginBottom: verticalScale(8),
    fontWeight: '400',
  },
  arrowIcon: {
    marginLeft: moderateScale(10),
    marginTop: verticalScale(6),
  },
  discountBadge: {
    backgroundColor: theme.colors.cashbackBg,
    borderWidth: 1,
    borderColor: theme.colors.cashbackBorder,
    borderRadius: moderateScale(6),
    paddingHorizontal: moderateScale(12),
    paddingVertical: verticalScale(4),
    alignSelf: 'flex-start',
    marginTop: 0,
  },
  discountText: {
    color: theme.colors.cashbackText,
    fontWeight: '600',
    fontSize: moderateScale(13),
  },
  loadingSpinner: {
    marginTop: verticalScale(32),
  },
  errorText: {
    color: 'red',
    margin: moderateScale(16),
    fontSize: moderateScale(16),
  },
  listContainer: {
    paddingBottom: verticalScale(8),
  },
}));

export default PopularStores;
