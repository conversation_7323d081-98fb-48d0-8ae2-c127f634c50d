import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { FlatList, Image, Text, TouchableOpacity, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import ArrowRight from 'react-native-vector-icons/SimpleLineIcons';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { makeStyles, useTheme } from '@rneui/themed';

import { AppDispatch, RootState } from '@/store';
import { fetchFeaturedStores } from '@/store/slices/cashbackSlice';
import { APP_ROUTES, AppStackNavigatorParamList } from '@/types/routes';

const CashbackShops = () => {
  const { theme } = useTheme();
  const styles = useStyles(theme);
  const { t } = useTranslation();
  const navigation =
    useNavigation<NativeStackNavigationProp<AppStackNavigatorParamList>>();
  const dispatch = useDispatch<AppDispatch>();
  const { featuredStores, featuredStoresLoading, featuredStoresError } =
    useSelector((state: RootState) => state.cashback);

  useEffect(() => {
    dispatch(fetchFeaturedStores({}));
  }, [dispatch]);

  if (featuredStoresLoading) {
    return null; // Or a loading indicator if needed
  }
  if (featuredStoresError) {
    return null; // Or error handling if needed
  }

  if (!featuredStores || featuredStores.length === 0) return null;

  return (
    <View>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Top Sneakers Stores</Text>
        <TouchableOpacity
          onPress={() =>
            navigation.navigate(APP_ROUTES.ALL_STORES, { featured: true })
          }
        >
          <Text style={styles.viewAll}>
            {t('home.viewAll')}{' '}
            <ArrowRight
              name="arrow-right"
              size={moderateScale(9)}
              color={theme.colors.primary}
            />
          </Text>
        </TouchableOpacity>
      </View>
      <FlatList
        horizontal
        data={featuredStores}
        keyExtractor={i => i.id}
        contentContainerStyle={{
          paddingHorizontal: moderateScale(16),
        }}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={styles.shopCircle}
            onPress={() =>
              navigation.navigate(APP_ROUTES.STORE_DETAIL, { storeId: item.id })
            }
          >
            <Image source={{ uri: item.logo }} style={styles.shopImage} />
            <Text style={styles.shopName}>{item.name}</Text>
          </TouchableOpacity>
        )}
        showsHorizontalScrollIndicator={false}
      />
    </View>
  );
};

const useStyles = makeStyles(theme => ({
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: verticalScale(10),
    marginBottom: verticalScale(10),
    paddingHorizontal: moderateScale(16),
  },
  sectionTitle: {
    fontFamily: 'Inter',
    fontWeight: '500',
    fontSize: moderateScale(18),
    lineHeight: moderateScale(23),
    letterSpacing: -0.18, // -1% of 18px
    color: theme.colors.foreground,
  },
  viewAll: {
    fontFamily: 'Inter',
    fontWeight: '500',
    fontSize: moderateScale(12),
    lineHeight: moderateScale(19),
    letterSpacing: -0.12, // -1% of 12px
    color: theme.colors.primary,
  },
  shopCircle: {
    alignItems: 'center',
    marginRight: moderateScale(16),
    marginBottom: verticalScale(16),
  },
  shopImage: {
    width: moderateScale(48),
    height: moderateScale(48),
    borderRadius: moderateScale(24),
    backgroundColor: theme.colors.neutralGrey,
  },
  shopName: {
    marginTop: verticalScale(4),
    fontSize: moderateScale(12),
    color: theme.colors.neutralGrey,
  },
}));

export default CashbackShops;
