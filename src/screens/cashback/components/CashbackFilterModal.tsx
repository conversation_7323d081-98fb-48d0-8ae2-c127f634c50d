import React, { useEffect, useState } from 'react';
import { FlatList, Text, TouchableOpacity, View } from 'react-native';
import { CountryCode, Flag } from 'react-native-country-picker-modal';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import { useSelector } from 'react-redux';
import { makeStyles, useTheme } from '@rneui/themed';

import { CircleCheckbox, StoreIcon } from '@/constants/svgs';
import { RootState } from '@/store';

const COUNTRY_MAP: Record<string, string> = {
  DE: 'Germany',
  FR: 'France',
  ES: 'Spain',
  IT: 'Italy',
  PL: 'Poland',
  JP: 'Japan',
  BR: 'Brazil',
  CN: 'China',
  UK: 'United Kingdom',
  BE: 'Belgium',
  NL: 'Netherlands',
  SK: 'Slovakia',
  CZ: 'Czech Republic',
  EN: 'United Kingdom', // fallback for English region
  // Add more as needed
};
const REGION_TO_COUNTRY_CODE: Record<string, string> = {
  EN: 'GB',
  UK: 'GB',
};
interface RegionItem {
  code: string;
  name: string;
  countryCode: CountryCode;
}

const CashbackFilterModal = ({
  onSelect,
}: {
  onSelect?: (region: RegionItem) => void;
}) => {
  const { theme } = useTheme();
  const styles = useStyles(theme);
  const stores = useSelector((state: RootState) => state.cashback.stores);
  const [regions, setRegions] = useState<RegionItem[]>([]);
  const [selected, setSelected] = useState<string | null>(null);

  useEffect(() => {
    const regionSet = new Set<string>();
    stores.forEach(store => {
      (store.store_regions || []).forEach(region => {
        if (region.region) regionSet.add(region.region);
      });
    });
    const regionArr: RegionItem[] = Array.from(regionSet).map(code => ({
      code,
      name: COUNTRY_MAP[code] || code,
      countryCode: (REGION_TO_COUNTRY_CODE[code] || code) as CountryCode,
    }));
    regionArr.sort((a, b) => a.name.localeCompare(b.name));
    setRegions(regionArr);
    if (regionArr.length > 0 && !selected) setSelected(regionArr[0].code);
  }, [stores]);

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <StoreIcon width={24} height={24} />
        <Text style={styles.headerTitle}>Store Location</Text>
      </View>
      <FlatList
        data={regions}
        keyExtractor={item => item.code}
        renderItem={({ item }) => {
          return (
            <TouchableOpacity
              style={[
                styles.item,
                selected === item.code && styles.selectedItem,
              ]}
              onPress={() => setSelected(item.code)}
              activeOpacity={0.7}
            >
              <View style={styles.flagNameRow}>
                <Flag
                  countryCode={item.countryCode}
                  flagSize={20}
                  withEmoji={true}
                  withFlagButton={true}
                />
                <Text style={styles.countryText}>{item.name}</Text>
              </View>
              {selected === item.code && (
                <View style={styles.checkboxContainer}>
                  <CircleCheckbox />
                </View>
              )}
            </TouchableOpacity>
          );
        }}
        style={styles.list}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContent}
      />
      <TouchableOpacity
        style={styles.applyButton}
        onPress={() => {
          const selectedRegion = regions.find(r => r.code === selected);
          if (selectedRegion) onSelect?.(selectedRegion);
        }}
        disabled={!selected}
      >
        <Text style={styles.applyButtonText}>Apply Filter</Text>
      </TouchableOpacity>
    </View>
  );
};

const useStyles = makeStyles(theme => ({
  container: {
    backgroundColor: theme.colors.background,
    borderTopLeftRadius: moderateScale(16),
    borderTopRightRadius: moderateScale(16),
    padding: moderateScale(16),
    minHeight: verticalScale(300),
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: verticalScale(12),
    borderBottomWidth: verticalScale(1),
    borderColor: theme.colors.border,
    paddingBottom: verticalScale(12),
  },
  headerTitle: {
    fontSize: moderateScale(18),
    fontWeight: '600',
    marginLeft: moderateScale(12),
    color: theme.colors.foreground,
  },
  list: { flex: 1 },
  listContent: { paddingBottom: verticalScale(24) },
  item: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: moderateScale(12),
    borderRadius: moderateScale(32),
    marginBottom: verticalScale(6),
    backgroundColor: theme.colors.background,
    borderWidth: verticalScale(1),
    borderColor: 'transparent',
  },
  selectedItem: {
    backgroundColor: theme.colors.yellowBackground,
    borderColor: theme.colors.primary,
    borderRadius: moderateScale(32),
  },
  flagNameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  countryText: {
    fontSize: moderateScale(16),
    color: theme.colors.foreground,
    marginLeft: moderateScale(12),
    fontWeight: '500',
  },
  checkboxContainer: {
    marginLeft: moderateScale(12),
    alignItems: 'center',
    justifyContent: 'center',
  },
  applyButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: moderateScale(24),
    margin: moderateScale(16),
    paddingVertical: verticalScale(12),
    alignItems: 'center',
    justifyContent: 'center',
  },
  applyButtonText: {
    color: theme.colors.background,
    fontWeight: 'bold',
    fontSize: moderateScale(16),
  },
}));

export default CashbackFilterModal;
