import React from 'react';
import { Text, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import { makeStyles, useTheme } from '@rneui/themed';

import { SingleSneaker } from '@/types/singleSneaker';

interface SneakerDetailsProps {
  sneaker: SingleSneaker;
}

const SneakerDetails: React.FC<SneakerDetailsProps> = ({ sneaker }) => {
  const { theme } = useTheme();
  const styles = useStyles(theme);

  return (
    <View style={styles.container}>
      {/* Product Info */}
      <View style={styles.infoSection}>
        <Text style={styles.model}>{sneaker.model}</Text>
        <Text style={styles.brand}>{sneaker.brand}</Text>
      </View>
      {/* Description */}
      <View style={styles.descSection}>
        <Text style={styles.sectionTitle}>Product description</Text>
        <Text style={styles.descText}>{sneaker.description}</Text>
        <Text style={styles.descBullet}>• Shown: {sneaker.colorway}</Text>
        <Text style={styles.descBullet}>• Style: {sneaker.sku}</Text>
      </View>
    </View>
  );
};

const useStyles = makeStyles(theme => ({
  container: {
    backgroundColor: theme.colors.background,
  },
  infoSection: {
    paddingHorizontal: moderateScale(16),
    marginBottom: verticalScale(8),
  },
  model: {
    fontSize: moderateScale(18),
    fontWeight: '700',
    color: theme.colors.black,
    marginBottom: verticalScale(2),
  },
  brand: {
    fontSize: moderateScale(13),
    color: theme.colors.grey2,
    marginBottom: verticalScale(2),
  },
  descSection: {
    borderRadius: moderateScale(12),
    marginHorizontal: moderateScale(12),
    marginBottom: verticalScale(12),
    padding: moderateScale(14),
    elevation: 1,
    borderWidth: moderateScale(1),
    borderColor: theme.colors.border,
  },
  sectionTitle: {
    fontSize: moderateScale(14),
    fontWeight: '600',
    color: theme.colors.black,
    marginBottom: verticalScale(6),
  },
  descText: {
    fontSize: moderateScale(13),
    color: theme.colors.grey2,
    marginBottom: verticalScale(6),
  },
  descBullet: {
    fontSize: moderateScale(12),
    color: theme.colors.grey2,
    marginBottom: verticalScale(2),
  },
}));

export default SneakerDetails;
