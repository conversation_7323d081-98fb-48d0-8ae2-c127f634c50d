import React from 'react';
import { TouchableOpacity, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/Feather';
import { useNavigation } from '@react-navigation/native';
import { makeStyles, useTheme } from '@rneui/themed';

const SingleSneakerHeader: React.FC = () => {
  const { theme } = useTheme();
  const styles = useStyles(theme);
  const navigation = useNavigation();

  return (
    <View style={styles.headerRow}>
      <TouchableOpacity
        style={styles.headerBtn}
        onPress={() => navigation.goBack()}
      >
        <Icon name="chevron-left" size={24} color={theme.colors.black} />
      </TouchableOpacity>
      <TouchableOpacity style={styles.headerBtn}>
        <Icon name="heart" size={22} color={theme.colors.black} />
      </TouchableOpacity>
    </View>
  );
};

const useStyles = makeStyles(theme => ({
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: moderateScale(12),
    paddingTop: verticalScale(8),
    marginBottom: verticalScale(8),
    backgroundColor: theme.colors.background,
  },
  headerBtn: {
    width: moderateScale(36),
    height: moderateScale(36),
    borderRadius: moderateScale(18),
    backgroundColor: theme.colors.white,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 2,
  },
}));

export default SingleSneakerHeader;
