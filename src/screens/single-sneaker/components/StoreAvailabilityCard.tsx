import React from 'react';
import { Image, Linking, Text, TouchableOpacity, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/Feather';
import { makeStyles, useTheme } from '@rneui/themed';

import { StoreAvailability } from '@/types/singleSneaker';

interface StoreAvailabilityCardProps {
  store: StoreAvailability;
}

const StoreAvailabilityCard: React.FC<StoreAvailabilityCardProps> = ({
  store,
}) => {
  const { theme } = useTheme();
  const styles = useStyles(theme);

  return (
    <View key={store.store_product_id} style={styles.storeCard}>
      <View style={styles.storeHeaderRow}>
        {/* Store logo */}
        <Image source={{ uri: store.store_logo }} style={styles.storeLogo} />
        <Text style={styles.storeName}>{store.store_name}</Text>
        <TouchableOpacity
          style={styles.storeExternalBtn}
          onPress={() =>
            store.product_url && Linking.openURL(store.product_url)
          }
        >
          <Icon name="external-link" size={20} color={theme.colors.black} />
        </TouchableOpacity>
      </View>
      <Text style={styles.storeCashback}>Upto 30% Cashback</Text>
      <View style={styles.priceRow}>
        <Text style={styles.storePrice}>${store.price}</Text>
        {store.is_on_sale && store.sale_price && (
          <Text style={styles.storeOldPrice}>${store.sale_price}</Text>
        )}
        <View
          style={[
            styles.storeDropType,
            store.is_launch ? styles.onlineDrop : styles.inStoreOnly,
          ]}
        >
          <Text
            style={
              store.is_launch ? styles.onlineDropText : styles.inStoreOnlyText
            }
          >
            {store.is_launch ? 'Online Drop' : 'In Store Only'}
          </Text>
        </View>
      </View>
      <Text style={styles.sizesLabel}>AVAILABLE SIZES</Text>
      <View style={styles.sizesList}>
        {store.available_sizes.map(size => (
          <View
            key={size.store_product_size_id}
            style={[
              styles.sizeBox,
              !size.available && styles.sizeBoxUnavailable,
            ]}
          >
            <Text style={styles.sizeText}>{size.us_size}</Text>
          </View>
        ))}
      </View>
    </View>
  );
};

const useStyles = makeStyles(theme => ({
  storeCard: {
    backgroundColor: theme.colors.white,
    borderRadius: moderateScale(12),
    marginBottom: verticalScale(12),
    padding: moderateScale(14),
    elevation: 0,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  storeHeaderRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: verticalScale(2),
  },
  storeLogo: {
    width: moderateScale(28),
    height: moderateScale(28),
    borderRadius: moderateScale(14),
    marginRight: moderateScale(8),
  },
  storeExternalBtn: {
    marginLeft: 'auto',
    width: moderateScale(36),
    height: moderateScale(36),
    borderRadius: moderateScale(18),
    backgroundColor: theme.colors.background,
    alignItems: 'center',
    justifyContent: 'center',
  },
  storeName: {
    fontSize: moderateScale(15),
    fontWeight: '600',
    color: theme.colors.black,
  },
  storeCashback: {
    fontSize: moderateScale(13),
    color: theme.colors.grey2,
    fontWeight: '500',
    marginBottom: verticalScale(4),
  },
  priceRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: verticalScale(6),
  },
  storePrice: {
    fontSize: moderateScale(22),
    fontWeight: '700',
    color: theme.colors.black,
    marginRight: moderateScale(10),
  },
  storeOldPrice: {
    fontSize: moderateScale(18),
    color: theme.colors.error,
    textDecorationLine: 'line-through',
    marginRight: moderateScale(10),
  },
  storeDropType: {
    borderRadius: moderateScale(6),
    paddingHorizontal: moderateScale(10),
    paddingVertical: verticalScale(2),
    marginLeft: moderateScale(4),
    borderWidth: 1,
  },
  onlineDrop: {
    borderColor: theme.colors.greenDot,
    backgroundColor: theme.colors.cashbackBg,
  },
  inStoreOnly: {
    borderColor: theme.colors.secondary,
    backgroundColor: theme.colors.background,
  },
  onlineDropText: {
    color: theme.colors.cashbackText,
    fontWeight: '600',
    fontSize: moderateScale(13),
  },
  inStoreOnlyText: {
    color: theme.colors.secondary,
    fontWeight: '600',
    fontSize: moderateScale(13),
  },
  sizesLabel: {
    fontSize: moderateScale(12),
    color: theme.colors.grey2,
    marginBottom: verticalScale(2),
    marginTop: verticalScale(2),
  },
  sizesList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: moderateScale(6),
  },
  sizeBox: {
    minWidth: moderateScale(36),
    paddingHorizontal: moderateScale(8),
    paddingVertical: verticalScale(6),
    borderRadius: moderateScale(8),
    backgroundColor: theme.colors.white,
    alignItems: 'center',
    marginRight: moderateScale(6),
    marginBottom: verticalScale(4),
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  sizeBoxUnavailable: {
    backgroundColor: theme.colors.grey3,
    borderColor: theme.colors.grey3,
  },
  sizeText: {
    fontSize: moderateScale(15),
    color: theme.colors.black,
  },
}));

export default StoreAvailabilityCard;
