import React, { useRef } from 'react';
import {
  Dimensions,
  Image,
  NativeScrollEvent,
  NativeSyntheticEvent,
  ScrollView,
  TouchableOpacity,
  View,
} from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/Feather';
import { useNavigation } from '@react-navigation/native';
import { makeStyles, useTheme } from '@rneui/themed';

import { SneakerImage } from '@/types/singleSneaker';

const { width } = Dimensions.get('window');

interface SneakerImageCarouselProps {
  images: SneakerImage[];
  activeImage: number;
  setActiveImage: (index: number) => void;
}

const SneakerImageCarousel: React.FC<SneakerImageCarouselProps> = ({
  images,
  activeImage,
  setActiveImage,
}) => {
  const { theme } = useTheme();
  const styles = useStyles(theme);
  const navigation = useNavigation();
  const scrollViewRef = useRef<ScrollView>(null);

  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const scrollPosition = event.nativeEvent.contentOffset.x;
    const imageIndex = Math.round(scrollPosition / width);
    if (imageIndex !== activeImage) {
      setActiveImage(imageIndex);
    }
  };

  const renderDots = () => (
    <View style={styles.dotsRow}>
      {images.map((_, idx) => (
        <View
          key={idx}
          style={[styles.dot, idx === activeImage && styles.dotActive]}
        />
      ))}
    </View>
  );

  const handleThumbnailPress = (index: number) => {
    setActiveImage(index);
    scrollViewRef.current?.scrollTo({
      x: index * width,
      y: 0,
      animated: true,
    });
  };

  const renderThumbnails = () => (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      style={styles.thumbsRow}
    >
      {images.map((img, idx) => (
        <TouchableOpacity
          key={img.image_url}
          onPress={() => handleThumbnailPress(idx)}
          style={[styles.thumbWrap, idx === activeImage && styles.thumbActive]}
        >
          <Image source={{ uri: img.image_url }} style={styles.thumbImg} />
        </TouchableOpacity>
      ))}
    </ScrollView>
  );

  return (
    <View>
      <View style={styles.imageCarousel}>
        {/* Header buttons overlaid on image - fixed position */}
        <View style={styles.headerOverlay}>
          <TouchableOpacity
            style={styles.headerBtn}
            onPress={() => navigation.goBack()}
          >
            <Icon
              name="chevron-left"
              size={moderateScale(24)}
              color={theme.colors.black}
            />
          </TouchableOpacity>
          <TouchableOpacity style={styles.headerBtn}>
            <Icon
              name="heart"
              size={moderateScale(22)}
              color={theme.colors.black}
            />
          </TouchableOpacity>
        </View>

        <ScrollView
          ref={scrollViewRef}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onScroll={handleScroll}
          scrollEventThrottle={16}
          style={styles.imageScrollView}
        >
          {images.map(image => (
            <View key={image.image_url} style={styles.imageContainer}>
              <Image
                source={{ uri: image.image_url }}
                style={styles.mainImage}
              />
            </View>
          ))}
        </ScrollView>

        {renderDots()}
      </View>
      <View style={styles.thumbsRowContainer}>{renderThumbnails()}</View>
    </View>
  );
};

const useStyles = makeStyles(theme => ({
  imageCarousel: {
    alignItems: 'center',
    marginBottom: verticalScale(8),
    position: 'relative',
  },
  imageScrollView: {
    width: width,
    height: width,
  },
  imageContainer: {
    width: width,
    height: width,
    position: 'relative',
  },
  mainImage: {
    width: width,
    height: width,
    resizeMode: 'cover',
    backgroundColor: theme.colors.white,
  },
  headerOverlay: {
    position: 'absolute',
    top: verticalScale(12),
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: moderateScale(16),
    zIndex: 10,
  },
  headerBtn: {
    width: moderateScale(40),
    height: moderateScale(40),
    borderRadius: moderateScale(20),
    backgroundColor: theme.colors.white,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 3,
    shadowColor: theme.colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: moderateScale(4),
  },
  dotsRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: verticalScale(8),
  },
  dot: {
    width: moderateScale(8),
    height: moderateScale(8),
    borderRadius: moderateScale(4),
    backgroundColor: theme.colors.border,
    marginHorizontal: moderateScale(4),
  },
  dotActive: {
    width: moderateScale(18),
    backgroundColor: theme.colors.primary,
  },
  thumbsRowContainer: {
    backgroundColor: theme.colors.background,
    paddingVertical: moderateScale(10),
  },
  thumbsRow: {
    flexDirection: 'row',
    paddingHorizontal: moderateScale(10),
    marginBottom: verticalScale(8),
  },
  thumbWrap: {
    borderWidth: moderateScale(2),
    borderColor: theme.colors.background,
    borderRadius: moderateScale(8),
    marginRight: moderateScale(8),
    backgroundColor: theme.colors.white,
  },
  thumbActive: {
    borderColor: theme.colors.primary,
  },
  thumbImg: {
    width: moderateScale(80),
    height: moderateScale(80),
    borderRadius: moderateScale(4),
    resizeMode: 'cover',
  },
}));

export default SneakerImageCarousel;
