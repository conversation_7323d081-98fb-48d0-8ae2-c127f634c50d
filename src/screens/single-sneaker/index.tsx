import React, { useEffect, useState } from 'react';
import { ScrollView, Text, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import { useDispatch, useSelector } from 'react-redux';
import { useRoute } from '@react-navigation/native';
import { makeStyles, useTheme } from '@rneui/themed';

import { SneakerDetailSkeleton } from '@/components/skeleton';
import { AppDispatch, RootState } from '@/store';
import { fetchSingleSneaker } from '@/store/slices/singleSneakerSlice';
import { SneakersStackNavigatorParamList } from '@/types/routes';

import {
  SneakerDetails,
  SneakerImageCarousel,
  StoreAvailabilityCard,
} from './components';

const SingleSneaker: React.FC = () => {
  const { theme } = useTheme();
  const styles = useStyles(theme);
  const route = useRoute();
  const { productId } =
    route.params as SneakersStackNavigatorParamList['SingleSneaker'];

  const dispatch = useDispatch<AppDispatch>();
  const { sneaker, loading, error } = useSelector(
    (state: RootState) => state.singleSneaker,
  );
  const [activeImage, setActiveImage] = useState(0);

  useEffect(() => {
    if (productId) {
      dispatch(fetchSingleSneaker(productId));
    }
  }, [dispatch, productId]);

  if (loading) {
    return <SneakerDetailSkeleton />;
  }
  if (error || !sneaker) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>Error: {error || 'Not found'}</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        <SneakerImageCarousel
          images={sneaker.images}
          activeImage={activeImage}
          setActiveImage={setActiveImage}
        />
        <SneakerDetails sneaker={sneaker} />
        <View style={styles.storeSection}>
          <Text style={styles.sectionTitle}>Store</Text>
          {sneaker.store_availability.map(store => (
            <StoreAvailabilityCard key={store.store_product_id} store={store} />
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const useStyles = makeStyles(theme => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  errorText: {
    color: theme.colors.error,
    fontSize: moderateScale(14),
    textAlign: 'center',
  },
  storeSection: {
    backgroundColor: theme.colors.background,
    marginHorizontal: moderateScale(12),
    marginBottom: verticalScale(16),
  },
  sectionTitle: {
    fontSize: moderateScale(14),
    fontWeight: '600',
    color: theme.colors.black,
    marginBottom: verticalScale(6),
  },
  scrollContent: {
    flexGrow: 1,
  },
}));

export default SingleSneaker;
