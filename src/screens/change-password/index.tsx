import React, { useState } from 'react';
import { Controller, useForm, useWatch } from 'react-hook-form';
import { TouchableOpacity, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { SafeAreaView } from 'react-native-safe-area-context';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/Feather';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import {
  Icon as RNEIcon,
  Input,
  makeStyles,
  Text,
  useTheme,
} from '@rneui/themed';

import AppButton from '@/components/AppButton';
import { AppStackNavigatorParamList } from '@/types/routes';

type ChangePasswordScreenNavigationProp = NativeStackNavigationProp<
  AppStackNavigatorParamList,
  keyof AppStackNavigatorParamList
>;

const ChangePasswordScreen = () => {
  const { theme } = useTheme();
  const styles = useStyles(theme);
  const navigation = useNavigation<ChangePasswordScreenNavigationProp>();

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
    mode: 'onChange',
  });

  const newPassword = useWatch({ control, name: 'newPassword' });

  const [currentPasswordVisible, setCurrentPasswordVisible] = useState(false);
  const [newPasswordVisible, setNewPasswordVisible] = useState(false);
  const [confirmPasswordVisible, setConfirmPasswordVisible] = useState(false);

  const handleSaveChanges = (data: {
    currentPassword: string;
    newPassword: string;
    confirmPassword: string;
  }) => {
    console.log('Save changes:', data);
    // TODO: Implement password change API call
    navigation.goBack();
  };

  const handleCancel = () => {
    navigation.goBack();
  };

  return (
    <SafeAreaView edges={['bottom']} style={styles.container}>
      <SafeAreaView
        edges={['top', 'left', 'right']}
        style={styles.headerSafeArea}
      >
        <LinearGradient
          colors={[theme.colors.foreground, theme.colors.brownGradient]}
        >
          <View style={styles.headerContent}>
            <View style={styles.headerRow}>
              <TouchableOpacity
                style={styles.backButton}
                onPress={() => navigation.goBack()}
              >
                <Icon
                  name="chevron-left"
                  size={moderateScale(24)}
                  color={theme.colors.white}
                />
              </TouchableOpacity>
              <Text style={styles.headerTitle}>Change Password</Text>
              <View style={styles.placeholder} />
            </View>
          </View>
        </LinearGradient>
      </SafeAreaView>

      <View style={styles.content}>
        <View style={styles.card}>
          <View style={styles.form}>
            <Text style={styles.label}>Current Password</Text>
            <Controller
              control={control}
              name="currentPassword"
              rules={{ required: 'Current password is required' }}
              render={({ field: { onChange, value } }) => (
                <Input
                  placeholder="Enter your current password"
                  value={value}
                  onChangeText={onChange}
                  secureTextEntry={!currentPasswordVisible}
                  errorMessage={errors.currentPassword?.message}
                  rightIcon={
                    <RNEIcon
                      name={currentPasswordVisible ? 'eye' : 'eye-off'}
                      type="feather"
                      size={20}
                      color={theme.colors.neutralGrey}
                      onPress={() =>
                        setCurrentPasswordVisible(!currentPasswordVisible)
                      }
                    />
                  }
                />
              )}
            />

            <Text style={styles.label}>New Password</Text>
            <Controller
              control={control}
              name="newPassword"
              rules={{
                required: 'New password is required',
                minLength: {
                  value: 8,
                  message: 'Password must be at least 8 characters',
                },
              }}
              render={({ field: { onChange, value } }) => (
                <Input
                  placeholder="Enter your new password"
                  value={value}
                  onChangeText={onChange}
                  secureTextEntry={!newPasswordVisible}
                  errorMessage={errors.newPassword?.message}
                  rightIcon={
                    <RNEIcon
                      name={newPasswordVisible ? 'eye' : 'eye-off'}
                      type="feather"
                      size={20}
                      color={theme.colors.neutralGrey}
                      onPress={() => setNewPasswordVisible(!newPasswordVisible)}
                    />
                  }
                />
              )}
            />

            <PasswordRequirements password={newPassword} />

            <Text style={styles.label}>Re-enter New Password</Text>
            <Controller
              control={control}
              name="confirmPassword"
              rules={{
                required: 'Please confirm your password',
                validate: (value, formValues) =>
                  value === formValues.newPassword || 'Passwords do not match',
              }}
              render={({ field: { onChange, value } }) => (
                <Input
                  placeholder="Confirm your new password"
                  value={value}
                  onChangeText={onChange}
                  secureTextEntry={!confirmPasswordVisible}
                  errorMessage={errors.confirmPassword?.message}
                  rightIcon={
                    <RNEIcon
                      name={confirmPasswordVisible ? 'eye' : 'eye-off'}
                      type="feather"
                      size={20}
                      color={theme.colors.neutralGrey}
                      onPress={() =>
                        setConfirmPasswordVisible(!confirmPasswordVisible)
                      }
                    />
                  }
                />
              )}
            />
          </View>
        </View>

        <View style={styles.buttonContainer}>
          <AppButton
            type="primary"
            size="large"
            onPress={handleSubmit(handleSaveChanges)}
            containerStyle={styles.saveButton}
          >
            <Text style={styles.saveButtonText}>Save Changes</Text>
          </AppButton>
          <AppButton
            type="ghost"
            size="large"
            onPress={handleCancel}
            containerStyle={styles.cancelButton}
          >
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </AppButton>
        </View>
      </View>
    </SafeAreaView>
  );
};

// Password Requirements Component
interface PasswordRequirementsProps {
  password: string;
}

const PasswordRequirements = ({ password }: PasswordRequirementsProps) => {
  const { theme } = useTheme();
  const styles = useStyles(theme);
  const requirements = [
    {
      label: 'Be minimum eight characters',
      valid: password && password.length >= 8,
    },
    {
      label: 'Have at least one number',
      valid: password && /[0-9]/.test(password),
    },
    {
      label: 'Have at least one special character',
      valid: password && /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password),
    },
  ];

  return (
    <View style={styles.requirementsContainer}>
      {requirements.map((req, idx) => (
        <View key={idx} style={styles.requirementRow}>
          <View
            style={[
              styles.requirementDot,
              req.valid && styles.requirementDotValid,
            ]}
          >
            {req.valid && (
              <Icon
                name="check"
                size={moderateScale(10)}
                color={theme.colors.white}
              />
            )}
          </View>
          <Text
            style={[
              styles.requirementText,
              req.valid && styles.requirementTextValid,
            ]}
          >
            {req.label}
          </Text>
        </View>
      ))}
    </View>
  );
};

const useStyles = makeStyles(theme => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  headerSafeArea: {
    backgroundColor: theme.colors.foreground,
  },
  headerContent: {
    paddingBottom: verticalScale(30),
    paddingTop: verticalScale(10),
    paddingHorizontal: moderateScale(16),
    gap: verticalScale(10),
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    marginBottom: verticalScale(12),
  },
  backButton: {
    width: moderateScale(40),
    height: moderateScale(40),
    borderRadius: moderateScale(20),
    backgroundColor: `${theme.colors.white}20`,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    color: theme.colors.white,
    fontSize: moderateScale(20),
    fontWeight: '500',
    marginLeft: moderateScale(10),
  },
  placeholder: {
    width: moderateScale(40),
  },
  content: {
    flex: 1,
    paddingHorizontal: moderateScale(20),
    marginTop: verticalScale(-20),
  },
  card: {
    backgroundColor: theme.colors.white,
    borderRadius: moderateScale(16),
    paddingVertical: verticalScale(16),
    paddingHorizontal: moderateScale(16),
    marginBottom: verticalScale(20),
    shadowColor: theme.colors.black,
    shadowOffset: {
      width: 0,
      height: verticalScale(2),
    },
    shadowOpacity: 0.1,
    shadowRadius: moderateScale(8),
    elevation: 4,
  },
  form: {
    width: '100%',
  },
  label: {
    fontSize: moderateScale(16),
    fontWeight: '500',
    color: theme.colors.foreground,
    marginBottom: verticalScale(8),
    marginTop: verticalScale(16),
  },
  requirementsContainer: {
    marginTop: verticalScale(2),
    marginBottom: verticalScale(6),
  },
  requirementRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: verticalScale(8),
  },
  requirementDot: {
    width: moderateScale(16),
    height: moderateScale(16),
    borderRadius: moderateScale(8),
    backgroundColor: theme.colors.grey5,
    marginRight: moderateScale(12),
    justifyContent: 'center',
    alignItems: 'center',
  },
  requirementDotValid: {
    backgroundColor: theme.colors.primary,
  },
  requirementText: {
    color: theme.colors.neutralGrey,
    fontSize: moderateScale(14),
  },
  requirementTextValid: {
    color: theme.colors.foreground,
    fontWeight: '500',
  },
  buttonContainer: {
    gap: verticalScale(12),
  },
  saveButton: {
    width: '100%',
  },
  saveButtonText: {
    fontSize: moderateScale(16),
    fontWeight: '600',
    color: theme.colors.white,
  },
  cancelButton: {
    width: '100%',
    backgroundColor: theme.colors.white,
  },
  cancelButtonText: {
    fontSize: moderateScale(16),
    fontWeight: '600',
    color: theme.colors.foreground,
  },
}));

export default ChangePasswordScreen;
