import React from 'react';
import { useTranslation } from 'react-i18next';
import { Image, Text, TouchableOpacity, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { SafeAreaView } from 'react-native-safe-area-context';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/Feather';
import { useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { makeStyles, useTheme } from '@rneui/themed';

import { homeData } from '@/constants/home';
import { RootState } from '@/store';
import { APP_ROUTES, AppStackNavigatorParamList } from '@/types/routes';

const HomeHeader = () => {
  const { theme } = useTheme();
  const styles = useStyles(theme);
  const { t } = useTranslation();
  const { user, balance } = useSelector((state: RootState) => state.home);
  const navigation = useNavigation<NativeStackNavigationProp<AppStackNavigatorParamList>>();

  return (
    <SafeAreaView edges={['top']} style={styles.headerSafeArea}>
      <LinearGradient
        colors={[theme.colors.foreground, theme.colors.brownGradient]}
      >
        <View style={styles.headerContent}>
          <View style={styles.headerRow}>
            <View style={styles.headerUserRow}>
              <Image source={homeData.userInfo.avatar} style={styles.avatar} />
              <View>
                <Text style={styles.welcome}>
                  {t('home.welcome')} {user?.email}
                </Text>
                <Text style={styles.welcomeBack}>{t('home.welcomeBack')}</Text>
              </View>
            </View>
            <TouchableOpacity
              onPress={() => navigation.navigate(APP_ROUTES.NOTIFICATION)}
            >
              <Icon
                name="bell"
                size={24}
                color={theme.colors.white}
                style={styles.bellIcon}
              />
            </TouchableOpacity>
          </View>

          <View style={styles.balanceRewardsRow}>
            {/* Cashback Balance */}
            <View style={styles.balanceContainer}>
              <Text style={styles.balanceLabel}>
                {t('home.cashbackBalance')}
              </Text>
              <Text style={styles.balanceAmount}>
                {balance?.['circle-chart'].approved ?? 0}
              </Text>
              <View style={styles.statusesRow}>
                <View style={styles.statusRow}>
                  <View
                    style={[
                      styles.dot,
                      { backgroundColor: theme.colors.greenDot },
                    ]}
                  />
                  <Text style={styles.approved}>
                    {t('home.approved')}
                    <Text style={styles.amount}>
                      {' '}
                      {balance?.['circle-chart'].approved ?? 0}
                    </Text>
                  </Text>
                </View>
                <View style={styles.statusRow}>
                  <View
                    style={[
                      styles.dot,
                      { backgroundColor: theme.colors.primary },
                    ]}
                  />
                  <Text style={styles.pending}>
                    {t('home.pending')}
                    <Text style={styles.amount}>
                      {' '}
                      {balance?.['circle-chart'].pending ?? 0}
                    </Text>
                  </Text>
                </View>
              </View>
            </View>
            {/* Rewards and Referral */}
            <View style={styles.rewardsRow}>
              <TouchableOpacity
                style={styles.rewardItem}
                onPress={() => navigation.navigate(APP_ROUTES.REWARDS)}
              >
                <View style={styles.rewardCircle}>
                  <Icon name="gift" size={20} color={theme.colors.white} />
                </View>
                <Text style={styles.rewardText}>{t('home.rewards')}</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.rewardItem}
                onPress={() => console.log('Referral pressed')}
              >
                <View style={styles.rewardCircle}>
                  <Icon name="user-plus" size={20} color={theme.colors.white} />
                </View>
                <Text style={styles.rewardText}>{t('home.referral')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </LinearGradient>
    </SafeAreaView>
  );
};
const useStyles = makeStyles(theme => ({
  avatar: {
    width: moderateScale(40),
    height: moderateScale(40),
    borderRadius: moderateScale(20),
    marginRight: moderateScale(12),
  },
  welcome: {
    fontWeight: '400',
    fontFamily: 'Inter',
    fontSize: moderateScale(12),
    color: theme.colors.white,
  },
  welcomeBack: {
    fontWeight: '600',
    fontFamily: 'Inter',
    fontSize: moderateScale(18),
    color: theme.colors.white,
  },
  balanceContainer: {
    flex: 1,
    borderRadius: moderateScale(12),
    paddingVertical: verticalScale(4),
    marginRight: moderateScale(16),
  },
  balanceLabel: {
    color: theme.colors.neutralGrey,
    fontSize: moderateScale(12),
    fontWeight: '400',
    fontFamily: 'Inter',
  },
  balanceAmount: {
    color: theme.colors.white,
    fontSize: moderateScale(32),
    fontWeight: '600',
    fontFamily: 'Inter',
  },
  approved: {
    color: theme.colors.neutralGrey,
    fontSize: moderateScale(10),
    fontFamily: 'Inter',
    fontWeight: '400',
  },
  pending: {
    color: theme.colors.neutralGrey,
    fontSize: moderateScale(10),
    fontFamily: 'Inter',
    fontWeight: '400',
  },
  amount: {
    color: theme.colors.white,
    fontSize: moderateScale(10),
    fontFamily: 'Inter',
    fontWeight: '500',
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  bellIcon: {
    marginLeft: moderateScale(8),
  },
  statusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: moderateScale(6),
  },
  dot: {
    width: moderateScale(8),
    height: moderateScale(8),
    borderRadius: moderateScale(2),
    marginRight: moderateScale(4),
    backgroundColor: theme.colors.greenDot,
  },
  balanceRewardsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  rewardsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: moderateScale(16),
  },
  rewardItem: {
    alignItems: 'center',
    marginLeft: moderateScale(12),
  },
  rewardCircle: {
    width: moderateScale(40),
    height: moderateScale(40),
    borderRadius: moderateScale(100),
    backgroundColor: `${theme.colors.white}15`,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: verticalScale(4),
  },
  rewardText: {
    color: theme.colors.white,
    opacity: 0.7,
    fontSize: moderateScale(10),
    fontWeight: '400',
    fontFamily: 'Inter',
  },
  headerSafeArea: {
    backgroundColor: theme.colors.foreground,
  },
  headerContent: {
    paddingBottom: verticalScale(50),
    paddingTop: verticalScale(10),
    paddingHorizontal: moderateScale(16),
    gap: verticalScale(10),
  },
  headerUserRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusesRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: verticalScale(4),
    gap: moderateScale(12),
  },
  someStyle: {
    marginTop: verticalScale(4),
  },
}));

export default HomeHeader;
