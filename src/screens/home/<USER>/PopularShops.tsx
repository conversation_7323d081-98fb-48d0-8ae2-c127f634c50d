import React from 'react';
import { useTranslation } from 'react-i18next';
import { FlatList, Image, Text, TouchableOpacity, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import ArrowRight from 'react-native-vector-icons/SimpleLineIcons';
import { useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { makeStyles, useTheme } from '@rneui/themed';

import { RootState } from '@/store';
import { APP_ROUTES, AppStackNavigatorParamList } from '@/types/routes';

const PopularShops = () => {
  const { theme } = useTheme();
  const styles = useStyles(theme);
  const { t } = useTranslation();
  const { data } = useSelector((state: RootState) => state.home);
  const navigation =
    useNavigation<NativeStackNavigationProp<AppStackNavigatorParamList>>();

  if (!data?.promoted_stores) return null;

  const handleViewAll = () => {
    navigation.navigate(APP_ROUTES.ALL_STORES, { featured: false });
  };

  return (
    <View>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>{t('home.popularShops')}</Text>
        <TouchableOpacity onPress={handleViewAll}>
          <Text style={styles.viewAll}>
            {t('home.viewAll')}{' '}
            <ArrowRight
              name="arrow-right"
              size={9}
              color={theme.colors.primary}
            />
          </Text>
        </TouchableOpacity>
      </View>
      <FlatList
        horizontal
        data={data.promoted_stores}
        keyExtractor={i => i.id}
        contentContainerStyle={{
          paddingHorizontal: moderateScale(16),
        }}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={styles.shopCircle}
            onPress={() =>
              navigation.navigate(APP_ROUTES.STORE_DETAIL, { storeId: item.id })
            }
          >
            <Image source={{ uri: item.logo }} style={styles.shopImage} />
            <Text style={styles.shopName}>{item.name}</Text>
          </TouchableOpacity>
        )}
        showsHorizontalScrollIndicator={false}
      />
    </View>
  );
};

const useStyles = makeStyles(theme => ({
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: verticalScale(10),
    marginBottom: verticalScale(10),
    paddingHorizontal: moderateScale(16),
  },
  sectionTitle: {
    fontWeight: '500',
    fontSize: moderateScale(18),
    fontFamily: 'Inter',
    color: theme.colors.foreground,
  },
  viewAll: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: moderateScale(4),
    color: theme.colors.primary,
    fontWeight: '500',
    fontSize: moderateScale(12),
    fontFamily: 'Inter',
  },
  shopCircle: {
    alignItems: 'center',
    marginRight: moderateScale(16),
    marginBottom: verticalScale(16),
  },
  shopImage: {
    width: moderateScale(48),
    height: moderateScale(48),
    borderRadius: moderateScale(24),
    backgroundColor: theme.colors.neutralGrey,
  },
  shopName: {
    marginTop: verticalScale(4),
    fontSize: moderateScale(12),
    color: theme.colors.neutralGrey,
  },
}));

export default PopularShops;
