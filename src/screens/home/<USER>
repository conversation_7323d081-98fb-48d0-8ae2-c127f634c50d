import React, { JSX, useEffect } from 'react';
import { FlatList, Text, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { makeStyles, useTheme } from '@rneui/themed';

import { HomeSkeleton } from '@/components';
import { sections } from '@/constants/home';
import { AppDispatch, RootState } from '@/store';
import {
  fetchBalanceSummary,
  fetchHomeData,
  fetchUserProfile,
} from '@/store/slices/homeSlice';
import { SECTION_KEY } from '@/types/home';

import {
  CashbackOffers,
  HomeBanner,
  HomeHeader,
  PopularShops,
  UpcomingDrops,
} from './components';

const Home: React.FC = () => {
  const { theme } = useTheme();
  const styles = useStyles(theme);

  const dispatch = useDispatch<AppDispatch>();
  const { data, loading, error } = useSelector(
    (state: RootState) => state.home,
  );

  useEffect(() => {
    dispatch(fetchHomeData());
    dispatch(fetchUserProfile());
    dispatch(fetchBalanceSummary());
  }, [dispatch]);

  if (loading) {
    return <HomeSkeleton />;
  }
  if (error || !data) {
    return (
      <View style={styles.loaderContainer}>
        <Text style={styles.errorText}>Error: {error}</Text>
      </View>
    );
  }

  const renderSection = ({
    item,
  }: {
    item: SECTION_KEY;
  }): JSX.Element | null => {
    switch (item) {
      case SECTION_KEY.HEADER:
        return <HomeHeader />;
      case SECTION_KEY.BANNER:
        return <HomeBanner />;
      case SECTION_KEY.UPCOMING_DROPS:
        return <UpcomingDrops />;
      case SECTION_KEY.CASHBACK_OFFERS:
        return <CashbackOffers />;
      case SECTION_KEY.POPULAR_SHOPS:
        return <PopularShops />;
      default:
        return null;
    }
  };

  return (
    <View style={styles.safeArea}>
      <FlatList
        data={sections}
        keyExtractor={i => i.toString()}
        renderItem={renderSection}
        showsVerticalScrollIndicator={false}
        bounces={false}
      />
    </View>
  );
};

const useStyles = makeStyles(theme => ({
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    color: theme.colors.error,
  },
  safeArea: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
}));

export default Home;
