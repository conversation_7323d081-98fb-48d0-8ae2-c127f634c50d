import React, { JSX } from 'react';
import { useTranslation } from 'react-i18next';
import { Image, Pressable, Text, TouchableOpacity, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import ArrowRight from 'react-native-vector-icons/SimpleLineIcons';
import { useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { makeStyles, useTheme } from '@rneui/themed';

import { RootState } from '@/store';
import { APP_ROUTES, AppStackNavigatorParamList } from '@/types/routes';

const CashbackOffers = () => {
  const { theme } = useTheme();
  const styles = useStyles(theme);
  const { t } = useTranslation();
  const { data } = useSelector((state: RootState) => state.home);
  const navigation =
    useNavigation<NativeStackNavigationProp<AppStackNavigatorParamList>>();

  if (!data?.promoted_offers) return null;

  const handleViewAll = () => {
    navigation.navigate(APP_ROUTES.ALL_OFFERS);
  };

  const rows: JSX.Element[] = [];
  for (let i = 0; i < data.promoted_offers.length; i += 3) {
    const chunk = data.promoted_offers.slice(i, i + 3);
    rows.push(
      <View key={i} style={styles.cashbackRow}>
        {chunk.map(offer => (
          <Pressable
            key={offer.id}
            style={styles.cashbackCard}
            onPress={() =>
              navigation.navigate(APP_ROUTES.DEAL_DETAIL, {
                deal: offer as import('@/types/deals').PromotedOffer,
              })
            }
          >
            <Image
              source={{ uri: offer.store.logo }}
              style={styles.cashbackImage}
            />
            <Text style={styles.cashbackBrand}>{offer.store.name}</Text>
            <Text style={styles.cashbackType}>{offer.title}</Text>
            <View style={styles.cashbackBadge}>
              <Text style={styles.cashbackBadgeText}>
                {offer.voucher.promotion_percent}% {t('home.cashback')}
              </Text>
            </View>
          </Pressable>
        ))}
      </View>,
    );
  }

  return (
    <View>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>{t('home.promotedCashback')}</Text>
        <TouchableOpacity onPress={handleViewAll}>
          <Text style={styles.viewAll}>
            {t('home.viewAll')}{' '}
            <ArrowRight
              name="arrow-right"
              size={9}
              color={theme.colors.primary}
            />
          </Text>
        </TouchableOpacity>
      </View>
      <View style={styles.cashbackRowsContainer}>{rows}</View>
    </View>
  );
};

const useStyles = makeStyles(theme => ({
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: verticalScale(10),
    marginBottom: verticalScale(10),
    paddingHorizontal: moderateScale(16),
  },
  sectionTitle: {
    fontWeight: '500',
    fontSize: moderateScale(18),
    fontFamily: 'Inter',
    color: theme.colors.foreground,
  },
  viewAll: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: moderateScale(4),
    color: theme.colors.primary,
    fontWeight: '500',
    fontSize: moderateScale(12),
    fontFamily: 'Inter',
  },
  cashbackRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: verticalScale(8),
  },
  cashbackCard: {
    backgroundColor: theme.colors.white,
    borderRadius: moderateScale(4),
    alignItems: 'center',
    marginBottom: verticalScale(4),
    paddingVertical: verticalScale(12),
    paddingHorizontal: moderateScale(8),
    elevation: 2,
    shadowColor: theme.colors.black,
    shadowOffset: { width: 0, height: verticalScale(2) },
    shadowOpacity: 0.08,
    shadowRadius: moderateScale(8),
    width: '31%',
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  cashbackImage: {
    width: '95%',
    height: moderateScale(45),
    resizeMode: 'contain',
  },
  cashbackBrand: {
    marginTop: verticalScale(2),
    fontWeight: '600',
    fontSize: moderateScale(12),
    color: theme.colors.black,
    fontFamily: 'Inter',
  },
  cashbackType: {
    color: theme.colors.neutralGrey,
    fontSize: moderateScale(8),
    fontFamily: 'Inter',
    fontWeight: '500',
  },
  cashbackBadge: {
    backgroundColor: theme.colors.cashbackBg,
    borderWidth: moderateScale(1),
    borderColor: theme.colors.cashbackBorder,
    borderRadius: moderateScale(5),
    paddingHorizontal: moderateScale(6),
    paddingVertical: verticalScale(3),
    marginTop: verticalScale(8),
  },
  cashbackBadgeText: {
    color: theme.colors.cashbackText,
    fontWeight: '500',
    fontSize: moderateScale(8),
    textAlign: 'center',
    fontFamily: 'sans-serif',
  },
  cashbackRowsContainer: {
    paddingHorizontal: moderateScale(16),
  },
}));

export default CashbackOffers;
