import React from 'react';
import { Image, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Swiper from 'react-native-swiper';
import { makeStyles, useTheme } from '@rneui/themed';

import { homeData } from '@/constants/home';

const HomeBanner = () => {
  const { theme } = useTheme();
  const styles = useStyles(theme);

  return (
    <View style={styles.bannerContainer}>
      <Swiper
        style={styles.swiper}
        showsPagination={true}
        dotColor={theme.colors.border}
        activeDotColor={theme.colors.primary}
        height={156}
        autoplay
        horizontal={true}
      >
        {homeData.bannerImages.map((img, index) => (
          <View key={index} style={styles.bannerSlide}>
            <Image source={img} style={styles.bannerImage} />
          </View>
        ))}
      </Swiper>
    </View>
  );
};

const useStyles = makeStyles(theme => ({
  bannerContainer: {
    marginTop: verticalScale(-32),
    marginHorizontal: moderateScale(16),
    borderRadius: moderateScale(12),
    overflow: 'hidden',
    shadowColor: theme.colors.greenShadow,
    shadowOffset: { width: 0, height: verticalScale(9) },
    shadowOpacity: 0.14,
    shadowRadius: moderateScale(23),
    elevation: 4,
    backgroundColor: theme.colors.white,
    zIndex: 1000,
  },
  swiper: {
    height: verticalScale(150),
  },
  bannerSlide: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.white,
  },
  bannerImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
}));

export default HomeBanner;
