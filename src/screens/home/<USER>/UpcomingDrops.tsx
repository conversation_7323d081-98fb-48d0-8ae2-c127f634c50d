import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  FlatList,
  Image,
  Pressable,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import ArrowRight from 'react-native-vector-icons/SimpleLineIcons';
import { useSelector } from 'react-redux';
import { AddCalander } from '@assets/images/svg/AddCalander';
import { makeStyles, useTheme } from '@rneui/themed';

import useTypeSafeNavigation from '@/hooks/useTypeSafeNavigation';
import { RootState } from '@/store';
import { APP_ROUTES } from '@/types/routes';
import { calcDaysLeft } from '@/utils/date';

const UpcomingDrops = () => {
  const { theme } = useTheme();
  const styles = useStyles(theme);
  const { t } = useTranslation();
  const { data } = useSelector((state: RootState) => state.home);
  const navigation = useTypeSafeNavigation();

  if (!data) return null;

  const isEmpty =
    !data.upcoming_launches || data.upcoming_launches.length === 0;

  const handleViewAll = () => {
    navigation.navigate(APP_ROUTES.SNEAKERS);
  };

  return (
    <View>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>{t('home.upcomingDrops')}</Text>
        {!isEmpty && (
          <TouchableOpacity onPress={handleViewAll}>
            <Text style={styles.viewAll}>
              {t('home.viewAll')}{' '}
              <ArrowRight
                name="arrow-right"
                size={9}
                color={theme.colors.primary}
              />
            </Text>
          </TouchableOpacity>
        )}
      </View>
      {isEmpty ? (
        <Text style={styles.emptyText}>{t('home.noUpcomingDrops')}</Text>
      ) : (
        <FlatList
          horizontal
          data={data.upcoming_launches}
          keyExtractor={i => i.launch_id}
          contentContainerStyle={{ paddingHorizontal: moderateScale(16) }}
          renderItem={({ item }) => {
            const days = calcDaysLeft(item.product.release_date);

            return (
              <Pressable style={styles.dropCard} onPress={() => {}}>
                <View style={styles.dropImageWrapper}>
                  <Image
                    source={{ uri: item.launch_image_url }}
                    style={styles.dropImage}
                  />
                  <View style={styles.daysLeftBadge}>
                    <Text style={styles.daysLeftBadgeText}>
                      {days} {t('home.daysLeft')}
                    </Text>
                  </View>
                </View>
                <Text style={styles.dropBrand}>{item.product.brand}</Text>
                <Text style={styles.dropName}>{item.product.model}</Text>
                <View style={styles.dropPriceRow}>
                  <Text style={styles.dropPrice}>{item.product.sku} </Text>
                  <Text style={styles.oldPrice}>{item.product.colorway}</Text>
                  <TouchableOpacity
                    style={styles.addToCartBtn}
                    onPress={() => {}}
                  >
                    <AddCalander
                      width={moderateScale(18)}
                      height={moderateScale(18)}
                    />
                  </TouchableOpacity>
                </View>
              </Pressable>
            );
          }}
          showsHorizontalScrollIndicator={false}
        />
      )}
    </View>
  );
};

const useStyles = makeStyles(theme => ({
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: verticalScale(10),
    marginBottom: verticalScale(10),
    paddingHorizontal: moderateScale(16),
  },
  sectionTitle: {
    fontWeight: '500',
    fontSize: moderateScale(18),
    fontFamily: 'Inter',
    color: theme.colors.foreground,
  },
  viewAll: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: moderateScale(4),
    color: theme.colors.primary,
    fontWeight: '500',
    fontSize: moderateScale(12),
    fontFamily: 'Inter',
  },
  dropCard: {
    width: moderateScale(141),
    backgroundColor: theme.colors.white,
    borderWidth: moderateScale(1.5),
    borderColor: theme.colors.border,
    borderRadius: moderateScale(12),
    marginRight: moderateScale(12),
    padding: moderateScale(12),
  },
  dropImageWrapper: {
    backgroundColor: theme.colors.white,
    borderRadius: moderateScale(16),
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: verticalScale(12),
    position: 'relative',
    height: verticalScale(100),
  },
  dropImage: {
    width: moderateScale(125),
    height: verticalScale(108),
    resizeMode: 'cover',
    alignSelf: 'center',
  },
  dropName: {
    fontWeight: '500',
    fontSize: moderateScale(12),
    color: theme.colors.black,
    fontFamily: 'Inter',
  },
  dropPrice: {
    fontSize: moderateScale(12),
    color: theme.colors.black,
    fontWeight: '600',
  },
  oldPrice: {
    textDecorationLine: 'line-through',
    color: theme.colors.neutralGrey,
    fontSize: moderateScale(12),
    fontWeight: '600',
  },
  daysLeftBadge: {
    position: 'absolute',
    top: verticalScale(2),
    right: moderateScale(2),
    backgroundColor: theme.colors.error,
    borderRadius: moderateScale(4),
    paddingHorizontal: moderateScale(8),
    paddingVertical: verticalScale(2),
    zIndex: 2,
  },
  daysLeftBadgeText: {
    color: theme.colors.white,
    fontSize: moderateScale(8),
    fontWeight: '500',
    fontFamily: 'Inter',
  },
  dropBrand: {
    color: theme.colors.grey1,
    fontSize: moderateScale(10),
    fontFamily: 'Inter',
    fontWeight: '400',
  },
  dropPriceRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: verticalScale(8),
    justifyContent: 'space-between',
  },
  addToCartBtn: {
    backgroundColor: theme.colors.white,
    borderWidth: moderateScale(1.5),
    borderColor: theme.colors.border,
    borderRadius: moderateScale(20),
    padding: verticalScale(8),
    elevation: 2,
    marginLeft: 'auto',
  },
  emptyText: {
    textAlign: 'center',
    color: theme.colors.neutralGrey,
    marginBottom: verticalScale(12),
  },
}));

export default UpcomingDrops;
