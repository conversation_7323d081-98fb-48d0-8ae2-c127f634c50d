import React from 'react';
import { Image, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/Feather';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { makeStyles, useTheme } from '@rneui/themed';

import { PromotedOffer } from '@/types/deals';

interface DealDetailScreenProps {
  route: {
    params: {
      deal: PromotedOffer;
    };
  };
  navigation: NativeStackNavigationProp<Record<string, object>>;
}

const DealDetailScreen: React.FC<DealDetailScreenProps> = ({
  route,
  navigation,
}) => {
  const { deal } = route.params;
  const { theme } = useTheme();
  const styles = useStyles(theme);

  return (
    <View style={styles.container}>
      <View style={styles.bannerContainer}>
        <Image source={{ uri: deal.banner_image }} style={styles.bannerImage} />

        {/* Navigation Buttons Overlaid on Banner */}
        <SafeAreaView edges={['top']} style={styles.headerOverlay}>
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => navigation.goBack()}
            >
              <Icon
                name="arrow-left"
                size={moderateScale(24)}
                color={theme.colors.black}
              />
            </TouchableOpacity>
            <TouchableOpacity style={styles.heartButton}>
              <Icon
                name="heart"
                size={moderateScale(24)}
                color={theme.colors.black}
              />
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </View>

      <ScrollView
        showsVerticalScrollIndicator={false}
        style={styles.scrollView}
      >
        {/* Deal Information */}
        <View style={styles.contentContainer}>
          {/* Deal Type Tag */}
          <View style={styles.dealTypeContainer}>
            <View style={styles.dealTypeTag}>
              <Text style={styles.dealTypeText}>
                In{' '}
                {deal.deal_type.charAt(0).toUpperCase() +
                  deal.deal_type.slice(1)}{' '}
                Offer
              </Text>
            </View>
          </View>

          {/* Deal Title */}
          <Text style={styles.dealTitle}>{deal.title}</Text>

          {/* Store Info */}
          <View style={styles.storeContainer}>
            <Text style={styles.dealDescription}>{deal.description}</Text>
          </View>

          {/* Information about Deal */}
          <View style={styles.dealInfoContainer}>
            <View style={styles.dealInfoHeader}>
              <Text style={styles.dealInfoTitle}>Information about Deal</Text>
              <TouchableOpacity>
                <Icon
                  name="chevron-up"
                  size={moderateScale(20)}
                  color={theme.colors.black}
                />
              </TouchableOpacity>
            </View>
            <View>
              {deal.terms && deal.terms.length > 0 ? (
                deal.terms.map((term, index) => (
                  <View key={index} style={styles.discountOffer}>
                    <View style={styles.discountTag}>
                      <Text style={styles.discountText}>
                        {term.discount_percent}% Discount
                      </Text>
                    </View>
                    <Text style={styles.discountCondition}>
                      {term.term_description}
                    </Text>
                  </View>
                ))
              ) : (
                <Text style={styles.noTermsText}>
                  No specific terms available
                </Text>
              )}
            </View>
          </View>

          {/* Time to wait for approval */}
          <View style={styles.approvalContainer}>
            <View style={styles.approvalHeader}>
              <Text style={styles.approvalTitle}>
                Time to wait for approval
              </Text>
              <TouchableOpacity>
                <Icon
                  name="chevron-up"
                  size={moderateScale(20)}
                  color={theme.colors.black}
                />
              </TouchableOpacity>
            </View>
            <View style={styles.approvalContent}>
              <View style={styles.approvalItem}>
                <View style={styles.approvalNumber}>
                  <Text style={styles.approvalNumberText}>1</Text>
                </View>
                <Text style={styles.approvalText}>
                  Deal appears with 'Pending' status within 1 day
                </Text>
                <TouchableOpacity style={styles.infoIcon}>
                  <Icon name="info" size={16} color={theme.colors.grey2} />
                </TouchableOpacity>
              </View>
              <View style={styles.approvalItem}>
                <View style={styles.approvalNumber}>
                  <Text style={styles.approvalNumberText}>2</Text>
                </View>
                <Text style={styles.approvalText}>
                  Deal is confirmed and credited within 60-90 days
                </Text>
                <TouchableOpacity style={styles.infoIcon}>
                  <Icon name="info" size={16} color={theme.colors.grey2} />
                </TouchableOpacity>
              </View>
            </View>
          </View>

          {/* Important Notice */}
          <View style={styles.noticeContainer}>
            <View style={styles.noticeHeader}>
              <Text style={styles.noticeTitle}>Important Notice</Text>
              <TouchableOpacity>
                <Icon
                  name="chevron-up"
                  size={moderateScale(20)}
                  color={theme.colors.black}
                />
              </TouchableOpacity>
            </View>
            <View style={styles.noticeContent}>
              {deal.notices && deal.notices.length > 0 ? (
                deal.notices.map((notice, index) => (
                  <View key={index} style={styles.noticeListItem}>
                    <View style={styles.noticeNumber}>
                      <Text style={styles.noticeNumberText}>{index + 1}</Text>
                    </View>
                    <Text style={styles.noticeItemText}>{notice}</Text>
                  </View>
                ))
              ) : (
                <Text style={styles.noNoticeText}>No important notices</Text>
              )}
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const useStyles = makeStyles(theme => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: moderateScale(16),
    paddingVertical: verticalScale(12),
    backgroundColor: 'transparent',
  },
  backButton: {
    width: moderateScale(40),
    height: moderateScale(40),
    borderRadius: moderateScale(20),
    backgroundColor: theme.colors.white,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: theme.colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: moderateScale(4),
    elevation: 3,
  },
  headerTitle: {
    fontSize: moderateScale(18),
    fontWeight: '600',
    color: theme.colors.black,
  },
  heartButton: {
    width: moderateScale(40),
    height: moderateScale(40),
    borderRadius: moderateScale(20),
    backgroundColor: theme.colors.white,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: theme.colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: moderateScale(4),
    elevation: 3,
  },
  bannerContainer: {
    position: 'relative',
    height: verticalScale(250),
  },
  bannerImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'contain',
  },
  hotBadge: {
    position: 'absolute',
    top: verticalScale(16),
    right: moderateScale(16),
    backgroundColor: theme.colors.error,
    paddingHorizontal: moderateScale(8),
    paddingVertical: verticalScale(4),
    borderRadius: moderateScale(4),
  },
  hotBadgeText: {
    color: theme.colors.white,
    fontSize: moderateScale(12),
    fontWeight: '600',
  },
  headerOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: moderateScale(16),
    backgroundColor: theme.colors.white,
  },
  dealTypeContainer: {
    marginBottom: verticalScale(12),
  },
  dealTypeTag: {
    backgroundColor: `${theme.colors.purpleButton}10`,
    borderWidth: 1,
    borderColor: theme.colors.purpleButton,
    paddingHorizontal: moderateScale(12),
    paddingVertical: verticalScale(6),
    borderRadius: moderateScale(6),
    alignSelf: 'flex-start',
  },
  dealTypeText: {
    color: theme.colors.purpleButtonText,
    fontSize: moderateScale(12),
    fontWeight: '500',
  },
  dealTitle: {
    fontSize: moderateScale(24),
    fontWeight: '700',
    color: theme.colors.black,
    marginBottom: verticalScale(16),
    lineHeight: moderateScale(32),
  },
  storeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: verticalScale(16),
  },
  storeLogo: {
    width: moderateScale(40),
    height: moderateScale(40),
    borderRadius: moderateScale(20),
    marginRight: moderateScale(12),
  },
  storeInfo: {
    flex: 1,
  },
  storeName: {
    fontSize: moderateScale(16),
    fontWeight: '600',
    color: theme.colors.black,
  },
  storeRegion: {
    fontSize: moderateScale(14),
    color: theme.colors.grey2,
    marginTop: verticalScale(2),
  },
  dealDescription: {
    fontSize: moderateScale(16),
    color: theme.colors.black,
    lineHeight: moderateScale(24),
    marginBottom: verticalScale(20),
  },
  description: {
    fontSize: moderateScale(16),
    color: theme.colors.black,
    lineHeight: moderateScale(24),
    marginBottom: verticalScale(20),
  },
  voucherContainer: {
    backgroundColor: theme.colors.grey5,
    padding: moderateScale(16),
    borderRadius: moderateScale(12),
    marginBottom: verticalScale(20),
  },
  voucherTitle: {
    fontSize: moderateScale(16),
    fontWeight: '600',
    color: theme.colors.black,
    marginBottom: verticalScale(8),
  },
  voucherCodeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: theme.colors.white,
    padding: moderateScale(12),
    borderRadius: moderateScale(8),
    marginBottom: verticalScale(8),
  },
  voucherCode: {
    fontSize: moderateScale(18),
    fontWeight: '700',
    color: theme.colors.primary,
    letterSpacing: moderateScale(2),
  },
  copyButton: {
    padding: moderateScale(8),
  },
  voucherDescription: {
    fontSize: moderateScale(14),
    color: theme.colors.grey2,
  },
  termsContainer: {
    marginBottom: verticalScale(20),
  },
  sectionTitle: {
    fontSize: moderateScale(18),
    fontWeight: '600',
    color: theme.colors.black,
    marginBottom: verticalScale(12),
  },
  termItem: {
    marginBottom: verticalScale(8),
  },
  termDescription: {
    fontSize: moderateScale(14),
    color: theme.colors.black,
    lineHeight: moderateScale(20),
  },
  discountPercent: {
    fontSize: moderateScale(14),
    fontWeight: '600',
    color: theme.colors.primary,
    marginTop: verticalScale(4),
  },
  noticesContainer: {
    marginBottom: verticalScale(20),
  },
  noticeItem: {
    marginBottom: verticalScale(8),
  },
  noticeText: {
    fontSize: moderateScale(14),
    color: theme.colors.black,
    lineHeight: moderateScale(20),
  },
  datesContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: verticalScale(20),
  },
  dateItem: {
    flex: 1,
  },
  dateLabel: {
    fontSize: moderateScale(14),
    color: theme.colors.grey2,
    marginBottom: verticalScale(4),
  },
  dateValue: {
    fontSize: moderateScale(16),
    fontWeight: '600',
    color: theme.colors.black,
  },
  categoriesContainer: {
    marginBottom: verticalScale(20),
  },
  categoriesList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: moderateScale(8),
  },
  categoryTag: {
    backgroundColor: theme.colors.grey5,
    paddingHorizontal: moderateScale(12),
    paddingVertical: verticalScale(6),
    borderRadius: moderateScale(16),
  },
  categoryText: {
    fontSize: moderateScale(14),
    color: theme.colors.black,
    fontWeight: '500',
  },
  dealInfoContainer: {
    backgroundColor: theme.colors.yellowBackground,
    borderWidth: 1,
    borderColor: theme.colors.yellow,
    borderRadius: moderateScale(12),
    padding: moderateScale(16),
    marginBottom: verticalScale(20),
  },
  dealInfoHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: verticalScale(12),
  },
  dealInfoTitle: {
    fontSize: moderateScale(16),
    fontWeight: '600',
    color: theme.colors.black,
  },

  discountOffer: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    marginBottom: verticalScale(8),
  },
  discountTag: {
    backgroundColor: theme.colors.yellow,
    paddingHorizontal: moderateScale(10),
    paddingVertical: verticalScale(4),
    borderRadius: moderateScale(6),
    marginBottom: verticalScale(6),
  },
  discountText: {
    fontSize: moderateScale(14),
    fontWeight: '600',
    color: theme.colors.white,
  },
  discountCondition: {
    fontSize: moderateScale(14),
    color: theme.colors.black,
    lineHeight: moderateScale(20),
    flex: 1,
    flexWrap: 'wrap',
  },
  noTermsText: {
    fontSize: moderateScale(14),
    color: theme.colors.grey2,
    textAlign: 'center',
  },
  approvalContainer: {
    backgroundColor: theme.colors.background,
    borderWidth: 1,
    borderColor: theme.colors.grey3,
    borderRadius: moderateScale(12),
    padding: moderateScale(16),
    marginBottom: verticalScale(20),
  },
  approvalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: verticalScale(12),
  },
  approvalTitle: {
    fontSize: moderateScale(16),
    fontWeight: '600',
    color: theme.colors.black,
  },
  approvalContent: {
    // Add styles for the collapsible content if needed
  },
  approvalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: verticalScale(8),
  },
  approvalNumber: {
    width: moderateScale(24),
    height: moderateScale(24),
    borderRadius: moderateScale(12),
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: moderateScale(8),
  },
  approvalNumberText: {
    fontSize: moderateScale(14),
    fontWeight: '600',
  },
  approvalText: {
    fontSize: moderateScale(14),
    color: theme.colors.black,
    lineHeight: moderateScale(20),
    flex: 1,
  },
  infoIcon: {
    padding: moderateScale(4),
  },
  noticeContainer: {
    backgroundColor: theme.colors.background,
    borderWidth: 1,
    borderColor: theme.colors.grey3,
    borderRadius: moderateScale(12),
    padding: moderateScale(16),
    marginBottom: verticalScale(20),
  },
  noticeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: verticalScale(12),
  },
  noticeTitle: {
    fontSize: moderateScale(16),
    fontWeight: '600',
    color: theme.colors.black,
  },
  noticeContent: {
    // Add styles for the collapsible content if needed
  },
  noticeListItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: verticalScale(8),
  },
  noticeNumber: {
    width: moderateScale(24),
    height: moderateScale(24),
    borderRadius: moderateScale(12),
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: moderateScale(8),
  },
  noticeNumberText: {
    fontSize: moderateScale(14),
    fontWeight: '600',
  },
  noticeItemText: {
    fontSize: moderateScale(14),
    color: theme.colors.black,
    lineHeight: moderateScale(20),
    flex: 1,
  },
  noNoticeText: {
    fontSize: moderateScale(14),
    color: theme.colors.grey2,
    textAlign: 'center',
  },
}));

export default DealDetailScreen;
