import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Animated, FlatList, Text, View } from 'react-native';
import RBSheet from 'react-native-raw-bottom-sheet';
import { SafeAreaView } from 'react-native-safe-area-context';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import { useDispatch, useSelector } from 'react-redux';
import { makeStyles, useTheme } from '@rneui/themed';

import { CategoryLoadingSkeleton, DealsSkeleton } from '@/components/skeleton';
import { AppDispatch, RootState } from '@/store';
import { fetchDeals } from '@/store/slices/dealsSlice';
import { PromotedOffer } from '@/types/deals';

import FilterModal from './components/FilterModal';
import { DealCard, DealsHeader } from './components';

interface RBSheetRef {
  open: () => void;
  close: () => void;
}

const Deals: React.FC = () => {
  const { theme } = useTheme();
  const styles = useStyles(theme);
  const refRBSheet = useRef<RBSheetRef>(null);
  const fadeAnim = useRef(new Animated.Value(0)).current;

  const dispatch = useDispatch<AppDispatch>();
  const { deals, productCategories, loading, categoryLoading, error } =
    useSelector((state: RootState) => state.deals);
  const [selectedCategory, setSelectedCategory] = useState<string>('All');

  useEffect(() => {
    dispatch(fetchDeals(undefined));
  }, [dispatch]);

  useEffect(() => {
    const timer = setTimeout(() => {
      if (selectedCategory === 'All') {
        dispatch(fetchDeals(undefined));
      } else {
        dispatch(fetchDeals(selectedCategory));
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [dispatch, selectedCategory]);

  useEffect(() => {
    if (!loading && !categoryLoading && deals.length > 0) {
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      fadeAnim.setValue(0);
    }
  }, [loading, categoryLoading, deals.length, fadeAnim]);

  const allCategories = useMemo(() => {
    return ['All', ...(productCategories || [])];
  }, [productCategories]);

  const filteredDeals = useMemo(() => {
    return deals;
  }, [deals, selectedCategory]);

  const gridData = useMemo<(PromotedOffer | null)[]>(() => {
    const arr: (PromotedOffer | null)[] = [...filteredDeals];
    if (arr.length % 2 === 1) arr.push(null);

    return arr;
  }, [filteredDeals]);

  if (loading && deals.length === 0) {
    return <DealsSkeleton />;
  }

  if (error) {
    return (
      <View style={styles.dealsLoaderContainer}>
        <Text style={styles.errorText}>Error loading deals: {error}</Text>
      </View>
    );
  }

  const renderContent = () => {
    if (categoryLoading) {
      return (
        <Animated.View style={[styles.animatedContainer]}>
          <CategoryLoadingSkeleton />
        </Animated.View>
      );
    }

    if (filteredDeals.length === 0) {
      return (
        <View style={styles.dealsLoaderContainer}>
          <Text style={styles.emptyText}>
            {selectedCategory === 'All'
              ? 'No deals found'
              : `No deals found for ${selectedCategory} category`}
          </Text>
        </View>
      );
    }

    return (
      <Animated.View style={[styles.animatedContainer, { opacity: fadeAnim }]}>
        <FlatList
          contentContainerStyle={styles.dealsContainer}
          data={gridData}
          keyExtractor={(item, idx) => (item ? item.id : `empty-${idx}`)}
          renderItem={({ item }) => <DealCard item={item} />}
          numColumns={2}
          columnWrapperStyle={styles.columnWrapper}
          showsVerticalScrollIndicator={false}
        />
      </Animated.View>
    );
  };

  return (
    <SafeAreaView edges={['bottom']} style={styles.container}>
      <DealsHeader
        categories={allCategories}
        selectedCategory={selectedCategory}
        setSelectedCategory={setSelectedCategory}
        onFilterPress={() => refRBSheet.current?.open()}
      />
      {renderContent()}
      <RBSheet
        ref={refRBSheet}
        height={verticalScale(450)}
        customStyles={{
          container: {
            borderTopLeftRadius: moderateScale(20),
            borderTopRightRadius: moderateScale(20),
          },
        }}
      >
        <FilterModal onClose={() => refRBSheet.current?.close()} />
      </RBSheet>
    </SafeAreaView>
  );
};

const useStyles = makeStyles(theme => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  dealsLoaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: moderateScale(20),
  },
  errorText: {
    color: theme.colors.error,
    fontSize: moderateScale(14),
    textAlign: 'center',
  },
  loadingText: {
    color: theme.colors.grey2,
    fontSize: moderateScale(14),
    marginTop: verticalScale(8),
    textAlign: 'center',
  },
  emptyText: {
    color: theme.colors.grey2,
    fontSize: moderateScale(16),
    textAlign: 'center',
  },
  dealsContainer: {
    paddingHorizontal: moderateScale(8),
    paddingTop: verticalScale(12),
    paddingBottom: verticalScale(20),
  },
  columnWrapper: {
    justifyContent: 'space-between',
  },
  animatedContainer: {
    flex: 1,
    opacity: 0.7,
  },
}));

export default Deals;
