import React from 'react';
import { Image, Pressable, Text, TouchableOpacity, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/Feather';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Card, makeStyles, useTheme } from '@rneui/themed';

import { TrendingTag } from '@/constants/svgs';
import { PromotedOffer } from '@/types/deals';
import { APP_ROUTES, AppStackNavigatorParamList } from '@/types/routes';

interface DealCardProps {
  item: PromotedOffer | null;
}

const DealCard: React.FC<DealCardProps> = ({ item }) => {
  const styles = useStyles();
  const { theme } = useTheme();
  const navigation =
    useNavigation<NativeStackNavigationProp<AppStackNavigatorParamList>>();

  if (item === null) {
    return <View style={styles.emptyCard} />;
  }

  const typeLabel = `${
    item.deal_type.charAt(0).toUpperCase() + item.deal_type.slice(1)
  } Offer`;

  return (
    <Pressable
      onPress={() =>
        navigation.navigate(APP_ROUTES.DEAL_DETAIL, {
          deal: item,
        })
      }
    >
      <Card
        containerStyle={styles.cardContainer}
        wrapperStyle={styles.cardWrapper}
      >
        {item.is_hot && (
          <View style={styles.trendingBadge}>
            <TrendingTag />
          </View>
        )}

        <View style={styles.dealImageContainer}>
          <Image source={{ uri: item.store.logo }} style={styles.dealImage} />
        </View>

        <View style={styles.dealInfo}>
          <Text style={styles.dealBrand}>{typeLabel}</Text>
          <Text style={styles.dealTitle} numberOfLines={1}>
            {item.title}
          </Text>

          <View style={styles.dealFooterRow}>
            <Text style={styles.dealFooterDiscount}>
              ⚡ {item.voucher.promotion_percent}% OFF
            </Text>
            <TouchableOpacity style={styles.dealFooterBtn}>
              <Icon name="chevron-right" size={16} color={theme.colors.black} />
            </TouchableOpacity>
          </View>
        </View>
      </Card>
    </Pressable>
  );
};

const useStyles = makeStyles(theme => ({
  emptyCard: {
    flex: 1,
    marginHorizontal: moderateScale(4),
    backgroundColor: 'transparent',
  },
  cardContainer: {
    borderRadius: moderateScale(8),
    flex: 1,
    marginHorizontal: moderateScale(4),
    marginBottom: verticalScale(0),
    elevation: 2,
    shadowColor: theme.colors.black,
    shadowOffset: { width: 0, height: verticalScale(2) },
    shadowOpacity: 0.1,
    shadowRadius: moderateScale(4),
    padding: 0,
  },
  cardWrapper: {
    margin: 0,
    padding: 0,
    position: 'relative',
  },
  trendingBadge: {
    position: 'absolute',
    top: verticalScale(16),
    left: moderateScale(-5),
    zIndex: 1,
  },
  dealImageContainer: {
    width: '90%',
    height: moderateScale(120),
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: moderateScale(6),
    overflow: 'hidden',
    marginTop: verticalScale(8),
    alignSelf: 'center',
  },
  dealImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  dealInfo: {
    padding: moderateScale(12),
  },
  dealBrand: {
    fontSize: moderateScale(10),
    color: theme.colors.grey2,
    marginBottom: verticalScale(4),
  },
  dealTitle: {
    fontSize: moderateScale(14),
    fontWeight: '600',
    color: theme.colors.black,
    marginBottom: verticalScale(8),
  },
  dealFooterRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dealFooterDiscount: {
    color: theme.colors.primary,
    fontWeight: '600',
    fontSize: moderateScale(14),
  },
  dealFooterBtn: {
    width: moderateScale(32),
    height: moderateScale(32),
    borderRadius: moderateScale(16),
    backgroundColor: theme.colors.white,
    borderColor: theme.colors.grey3,
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
}));

export default DealCard;
