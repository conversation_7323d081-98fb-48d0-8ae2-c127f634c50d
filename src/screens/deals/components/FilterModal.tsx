import React, { useEffect } from 'react';
import { ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/Feather';
import { useSelector } from 'react-redux';
import { CheckBox, makeStyles, useTheme } from '@rneui/themed';

import { FilterIconOutline, SortIcon } from '@/constants/svgs';
import { DealsService } from '@/services/deals';
import { RootState } from '@/store';
import { Region } from '@/types/deals';

type FilterModalHeaderProps = {
  onReset?: () => void;
  theme: ReturnType<typeof useTheme>['theme'];
  styles: ReturnType<typeof useStyles>;
};

const FilterModalHeader: React.FC<FilterModalHeaderProps> = ({
  onReset,
  theme,
  styles,
}) => (
  <View style={styles.headerContainer}>
    <View style={styles.dragIndicator} />
    <View style={styles.headerRow}>
      <View style={styles.headerTitleRow}>
        <FilterIconOutline
          color={theme.colors.grey1}
          style={styles.headerIcon}
        />
        <Text style={styles.headerTitle}>Filter</Text>
      </View>
      <TouchableOpacity onPress={onReset} style={styles.resetBtn}>
        <Text style={styles.resetText}>Reset</Text>
      </TouchableOpacity>
    </View>
    <View style={styles.headerDivider} />
  </View>
);

const FilterModal: React.FC<{ onClose?: () => void }> = ({ onClose }) => {
  const { productCategories } = useSelector((state: RootState) => state.deals);

  const [storeTypeChecks, setStoreTypeChecks] = React.useState<
    Record<string, boolean>
  >({});

  const [regions, setRegions] = React.useState<Region[]>([]);
  const [selectedRegion, setSelectedRegion] = React.useState<string>('');
  const [sortBy, setSortBy] = React.useState<string>('best_match');
  const [isLoadingRegions, setIsLoadingRegions] = React.useState(true);

  const { theme } = useTheme();
  const styles = useStyles(theme);

  const toggleStoreType = (key: string) => {
    setStoreTypeChecks(prev => ({ ...prev, [key]: !prev[key] }));
  };

  useEffect(() => {
    const initialChecks: Record<string, boolean> = {};
    productCategories.forEach(category => {
      initialChecks[category] = false;
    });
    setStoreTypeChecks(initialChecks);
  }, [productCategories]);

  useEffect(() => {
    setIsLoadingRegions(true);
    DealsService.getRegions()
      .then(data => {
        setRegions(data);
        if (data && data.length > 0) {
          setSelectedRegion(data[0].region_id);
        }
      })
      .catch(error => {
        console.error('Failed to load regions:', error);
        setRegions([]);
      })
      .finally(() => {
        setIsLoadingRegions(false);
      });
  }, []);

  const sortOptions = [
    { label: 'Best match', value: 'best_match' },
    { label: 'Popular', value: 'popular' },
    { label: 'Most Engaged', value: 'most_engaged' },
  ];

  return (
    <View style={styles.container}>
      <FilterModalHeader onReset={onClose} theme={theme} styles={styles} />
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Store Type */}
        <View style={styles.section}>
          <View style={styles.sectionHeaderRow}>
            <Text style={styles.sectionTitleBlack}>Store Type</Text>
            <Icon
              name="chevron-up"
              size={moderateScale(20)}
              color={theme.colors.black}
            />
          </View>
          {productCategories.map(category => (
            <View key={category} style={styles.checkboxRowCustom}>
              <CheckBox
                checked={storeTypeChecks[category] || false}
                onPress={() => toggleStoreType(category)}
                containerStyle={styles.checkBoxContainer}
                checkedColor={theme.colors.primary}
                uncheckedColor={theme.colors.border}
                iconType="material"
                checkedIcon="check-box"
                uncheckedIcon="check-box-outline-blank"
              />
              <Text style={styles.checkboxLabelBlack}>
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </Text>
            </View>
          ))}
          <TouchableOpacity>
            <Text style={styles.viewAllUnderline}>View All</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.divider} />

        <View style={styles.section}>
          <View style={styles.sectionHeaderRow}>
            <Text style={styles.sectionTitleBlack}>Offer Region</Text>
            <Icon
              name="chevron-up"
              size={moderateScale(20)}
              color={theme.colors.black}
            />
          </View>
          <View style={styles.regionDropdown}>
            {isLoadingRegions && (
              <Text style={styles.loadingText}>Loading regions...</Text>
            )}
            {!isLoadingRegions && regions.length === 0 && (
              <Text style={styles.errorText}>No regions available</Text>
            )}
            {!isLoadingRegions && regions.length > 0 && (
              <Dropdown
                style={styles.dropdown}
                placeholderStyle={styles.placeholderStyle}
                selectedTextStyle={styles.selectedTextStyle}
                inputSearchStyle={styles.inputSearchStyle}
                iconStyle={styles.iconStyle}
                data={regions.map(region => ({
                  label: region.name,
                  value: region.region_id,
                }))}
                search
                maxHeight={verticalScale(200)}
                labelField="label"
                valueField="value"
                placeholder="Select region"
                searchPlaceholder="Search..."
                value={selectedRegion}
                onChange={item => {
                  setSelectedRegion(item.value);
                }}
                renderLeftIcon={() => (
                  <Icon
                    name="map-pin"
                    size={moderateScale(20)}
                    color={theme.colors.grey1}
                  />
                )}
              />
            )}
          </View>
        </View>

        <View style={styles.divider} />

        {/* Sort By */}
        <View style={styles.section}>
          <View style={styles.sectionHeaderRow}>
            <View style={styles.sectionTitleRow}>
              <SortIcon color={theme.colors.grey1} style={styles.sectionIcon} />
              <Text style={styles.sectionTitleBlack}>Sort By</Text>
            </View>
            <Icon
              name="chevron-up"
              size={moderateScale(20)}
              color={theme.colors.black}
            />
          </View>
          {sortOptions.map(option => (
            <TouchableOpacity
              key={option.value}
              style={styles.radioRowCustom}
              onPress={() => setSortBy(option.value)}
            >
              <View style={styles.radioButton}>
                <View
                  style={[
                    styles.radioButtonOuter,
                    sortBy === option.value && styles.radioButtonSelected,
                  ]}
                >
                  {sortBy === option.value && (
                    <View style={styles.radioButtonInner} />
                  )}
                </View>
              </View>
              <Text style={styles.radioLabelBlack}>{option.label}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
      {/* Apply Filter Button */}
      <TouchableOpacity style={styles.applyButton} onPress={onClose}>
        <Text style={styles.applyButtonText}>Apply Filter</Text>
      </TouchableOpacity>
    </View>
  );
};

const useStyles = makeStyles(theme => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    borderTopLeftRadius: moderateScale(20),
    borderTopRightRadius: moderateScale(20),
    paddingBottom: verticalScale(16),
  },
  headerContainer: {
    paddingTop: verticalScale(8),
    backgroundColor: theme.colors.background,
    borderTopLeftRadius: moderateScale(20),
    borderTopRightRadius: moderateScale(20),
  },
  dragIndicator: {
    alignSelf: 'center',
    width: moderateScale(80),
    height: verticalScale(6),
    borderRadius: moderateScale(4),
    backgroundColor: theme.colors.border,
    marginBottom: verticalScale(6),
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: moderateScale(20),
    paddingBottom: verticalScale(8),
    paddingTop: verticalScale(4),
  },
  headerTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerIcon: {
    marginRight: moderateScale(8),
  },
  headerTitle: {
    fontSize: moderateScale(22),
    fontWeight: '400',
    color: theme.colors.foreground,
  },
  resetBtn: {
    alignSelf: 'center',
  },
  resetText: {
    color: theme.colors.primary,
    fontWeight: '400',
    fontSize: moderateScale(16),
    textDecorationLine: 'underline',
  },
  headerDivider: {
    height: verticalScale(1),
    backgroundColor: theme.colors.border,
    marginTop: verticalScale(8),
  },
  scrollContent: {
    padding: moderateScale(16),
    paddingBottom: verticalScale(60),
  },
  section: {
    marginBottom: verticalScale(16),
    backgroundColor: theme.colors.background,
    borderRadius: moderateScale(12),
    paddingBottom: verticalScale(6),
  },
  sectionHeaderRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: verticalScale(12),
  },
  sectionTitleBlack: {
    fontSize: moderateScale(18),
    fontWeight: '400',
    color: theme.colors.foreground,
  },
  regionDropdown: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: theme.colors.background,
    borderWidth: moderateScale(1),
    borderColor: theme.colors.border,
    borderRadius: moderateScale(8),
    paddingHorizontal: moderateScale(16),
    paddingVertical: verticalScale(6),
  },
  loadingText: {
    color: theme.colors.grey1,
    fontSize: moderateScale(16),
  },
  errorText: {
    color: theme.colors.grey1,
    fontSize: moderateScale(16),
  },
  dropdown: {
    height: verticalScale(50),
    borderWidth: 0,
    backgroundColor: 'transparent',
    borderRadius: moderateScale(8),
    paddingHorizontal: moderateScale(8),
    paddingVertical: verticalScale(6),
    width: '100%',
  },
  placeholderStyle: {
    fontSize: moderateScale(16),
    color: theme.colors.grey1,
  },
  selectedTextStyle: {
    fontSize: moderateScale(16),
    color: theme.colors.foreground,
  },
  inputSearchStyle: {
    height: verticalScale(40),
    fontSize: moderateScale(16),
    color: theme.colors.foreground,
  },
  iconStyle: {
    width: moderateScale(20),
    height: verticalScale(20),
    color: theme.colors.grey1,
  },

  checkboxRowCustom: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: verticalScale(6),
  },
  checkBoxContainer: {
    padding: 0,
    margin: 0,
  },
  checkboxLabelBlack: {
    fontSize: moderateScale(18),
    fontWeight: '400',
    color: theme.colors.foreground,
  },
  checkboxCount: {
    color: theme.colors.grey1,
    fontWeight: '400',
    fontSize: moderateScale(16),
  },
  viewAllUnderline: {
    color: theme.colors.primary,
    fontSize: moderateScale(16),
    marginTop: verticalScale(4),
    marginBottom: verticalScale(4),
    fontWeight: '400',
    textDecorationLine: 'underline',
  },
  radioRowCustom: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: verticalScale(8),
  },
  radioButton: {
    marginRight: moderateScale(12),
  },
  radioButtonOuter: {
    width: moderateScale(20),
    height: verticalScale(20),
    borderRadius: moderateScale(10),
    borderWidth: moderateScale(2),
    borderColor: theme.colors.border,
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioButtonSelected: {
    borderColor: theme.colors.primary,
  },
  radioButtonInner: {
    width: moderateScale(10),
    height: verticalScale(10),
    borderRadius: moderateScale(5),
    backgroundColor: theme.colors.primary,
  },
  radioLabelBlack: {
    fontSize: moderateScale(18),
    fontWeight: '400',
    color: theme.colors.foreground,
  },
  applyButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: moderateScale(24),
    margin: moderateScale(16),
    paddingVertical: verticalScale(12),
    alignItems: 'center',
    justifyContent: 'center',
  },
  applyButtonText: {
    color: theme.colors.background,
    fontWeight: 'bold',
    fontSize: moderateScale(16),
  },
  sectionIcon: {
    marginRight: moderateScale(8),
  },
  sectionTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  divider: {
    height: verticalScale(1),
    backgroundColor: theme.colors.border,
    marginVertical: verticalScale(8),
  },
}));

export default FilterModal;
