import React from 'react';
import {
  Platform,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { SafeAreaView } from 'react-native-safe-area-context';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import { Chip, makeStyles, useTheme } from '@rneui/themed';

import { FilterIconOutline } from '@/constants/svgs';

interface DealsHeaderProps {
  categories: string[];
  selectedCategory: string;
  setSelectedCategory: (category: string) => void;
  onFilterPress: () => void;
}

const DealsHeader: React.FC<DealsHeaderProps> = ({
  categories,
  selectedCategory,
  setSelectedCategory,
  onFilterPress,
}) => {
  const { theme } = useTheme();
  const styles = useStyles(theme);

  return (
    <SafeAreaView
      edges={['top', 'left', 'right']}
      style={styles.headerSafeArea}
    >
      <LinearGradient
        colors={[theme.colors.foreground, theme.colors.brownGradient]}
      >
        <View style={styles.headerContent}>
          <View style={styles.headerRow}>
            <Text style={styles.headerTitle}>Deals</Text>
            <TouchableOpacity
              style={styles.settingsBtn}
              onPress={onFilterPress}
            >
              <FilterIconOutline color={theme.colors.white} />
            </TouchableOpacity>
          </View>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.pillsScroll}
          >
            {categories.map(cat => {
              const active = cat === selectedCategory;

              return (
                <Chip
                  key={cat}
                  title={cat}
                  onPress={() => setSelectedCategory(cat)}
                  buttonStyle={[
                    styles.chipButton,
                    active && styles.chipButtonActive,
                  ]}
                  titleStyle={[
                    styles.chipText,
                    active && styles.chipTextActive,
                  ]}
                  containerStyle={styles.chipContainer}
                />
              );
            })}
          </ScrollView>
        </View>
      </LinearGradient>
    </SafeAreaView>
  );
};

const useStyles = makeStyles(theme => ({
  headerSafeArea: {
    backgroundColor: theme.colors.foreground,
  },
  headerContent: {
    paddingBottom: verticalScale(50),
    paddingTop: Platform.OS === 'ios' ? verticalScale(5) : verticalScale(10),
    paddingHorizontal: moderateScale(16),
    gap: verticalScale(10),
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: verticalScale(12),
  },
  headerTitle: {
    color: theme.colors.white,
    fontSize: moderateScale(20),
    fontWeight: '500',
  },
  settingsBtn: {
    borderWidth: moderateScale(1),
    borderColor: `${theme.colors.white}80`,
    borderRadius: moderateScale(100),
    padding: moderateScale(8),
  },
  pillsScroll: {},
  chipContainer: {
    marginRight: moderateScale(12),
  },
  chipButton: {
    paddingVertical: verticalScale(6),
    paddingHorizontal: moderateScale(16),
    borderRadius: moderateScale(4),
    backgroundColor: `${theme.colors.white}33`,
    borderWidth: 0,
    justifyContent: 'center',
  },
  chipButtonActive: {
    backgroundColor: theme.colors.warning,
  },
  chipText: {
    color: theme.colors.white,
    fontSize: moderateScale(14),
    fontWeight: '500',
  },
  chipTextActive: {
    color: theme.colors.black,
  },
}));

export default DealsHeader;
