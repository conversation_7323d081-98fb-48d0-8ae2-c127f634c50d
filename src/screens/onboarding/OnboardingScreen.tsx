import React, { useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Dimensions,
  FlatList,
  ImageBackground,
  ListRenderItemInfo,
  View,
  ViewToken,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { makeStyles, Text, useTheme } from '@rneui/themed';

import AppButton from '@/components/AppButton';
import { slides } from '@/constants/onboarding';
import { AUTH_ROUTES, AuthStackNavigatorParamList } from '@/types/routes';

const { width, height } = Dimensions.get('window');

export interface Slide {
  key: string;
  title: string;
  description: string;
  image: ReturnType<typeof require>;
}

interface OnboardingScreenProps {
  navigation: NativeStackNavigationProp<AuthStackNavigatorParamList>;
}

function OnboardingScreen({ navigation }: OnboardingScreenProps) {
  const [currentIndex, setCurrentIndex] = useState<number>(0);
  const flatListRef = useRef<FlatList>(null);
  const { theme } = useTheme();
  const { t } = useTranslation();

  const styles = useStyles(theme);

  const handleNext = () => {
    if (currentIndex < slides.length - 1) {
      flatListRef.current?.scrollToIndex({ index: currentIndex + 1 });
    } else {
      navigation.navigate(AUTH_ROUTES.SOCIAL_SIGNIN);
    }
  };

  const handleSkip = () => {
    navigation.navigate(AUTH_ROUTES.SOCIAL_SIGNIN);
  };

  const onViewableItemsChanged = useRef(
    ({ viewableItems }: { viewableItems: ViewToken<Slide>[] }) => {
      if (viewableItems.length > 0) {
        setCurrentIndex(viewableItems[0].index as number);
      }
    },
  ).current;

  const viewConfigRef = useRef({ viewAreaCoveragePercentThreshold: 50 });

  return (
    <View style={styles.container}>
      <SafeAreaView style={styles.skipButtonContainer} pointerEvents="box-none">
        <AppButton
          buttonStyle={styles.skipButton}
          type="ghost"
          size="small"
          title="Skip"
          onPress={handleSkip}
        />
      </SafeAreaView>
      <FlatList
        data={slides}
        ref={flatListRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onViewableItemsChanged={onViewableItemsChanged}
        viewabilityConfig={viewConfigRef.current}
        keyExtractor={item => item.key}
        renderItem={({ item }: ListRenderItemInfo<Slide>) => (
          <ImageBackground
            source={item.image}
            style={styles.image}
            resizeMode="cover"
          />
        )}
      />
      <SafeAreaView style={styles.bottomContainer}>
        <View style={styles.pagination}>
          {slides.map((_, i) => (
            <View
              key={i}
              style={[
                styles.dot,
                {
                  backgroundColor:
                    currentIndex === i
                      ? theme.colors.primary
                      : theme.colors.neutralGrey,
                },
              ]}
            />
          ))}
        </View>
        <View style={styles.textContainer}>
          <Text h3 style={styles.title}>
            {t('onboarding.title')}
          </Text>
          <Text style={styles.description}>{t('onboarding.description')}</Text>
        </View>
        <View style={styles.buttonContainer}>
          <AppButton
            type="primary"
            size="large"
            title="Next"
            onPress={handleNext}
          />
        </View>
      </SafeAreaView>
    </View>
  );
}

const useStyles = makeStyles(theme => ({
  container: {
    flex: 1,
    justifyContent: 'flex-start',
    position: 'relative',
  },
  buttonContainer: {
    paddingHorizontal: moderateScale(26),
    paddingVertical: verticalScale(24),
  },
  skipButtonContainer: {
    position: 'absolute',
    top: verticalScale(0),
    right: moderateScale(12),
    zIndex: 10,
  },
  skipButton: {
    borderColor: theme.colors.foreground,
  },
  image: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width: width,
    height: height,
  },
  textContainer: {
    alignItems: 'center',
    paddingHorizontal: moderateScale(24),
  },
  title: {
    textAlign: 'center',
    marginBottom: verticalScale(18),
    fontWeight: '600',
  },
  description: {
    textAlign: 'center',
    fontWeight: '400',
    fontSize: moderateScale(14),
    color: theme.colors.neutralGrey,
  },
  pagination: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: verticalScale(8),
    marginBottom: verticalScale(16),
  },
  dot: {
    height: moderateScale(8),
    width: moderateScale(8),
    borderRadius: moderateScale(4),
    marginHorizontal: moderateScale(4),
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
}));

export default OnboardingScreen;
