import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Image,
  Platform,
  SafeAreaView,
  StatusBar,
  Text,
  View,
} from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import { MailBoxIcon } from '@assets/images/svg/MailBox';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Button, makeStyles, useTheme } from '@rneui/themed';

import { AUTH_ROUTES, AuthStackNavigatorParamList } from '@/types/routes';

type CheckEmailScreenNavigationProp = NativeStackNavigationProp<
  AuthStackNavigatorParamList,
  keyof AuthStackNavigatorParamList
>;

const CheckEmail = ({
  navigation,
}: {
  navigation: CheckEmailScreenNavigationProp;
}) => {
  const { theme } = useTheme();
  const styles = useStyles(theme);
  const { t } = useTranslation();

  const email = '<EMAIL>';

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Image source={require('../../../assets/images/webp/mail.webp')} />
        <Text style={styles.heading}>{t('checkEmail.heading')}</Text>
        <Text style={styles.description}>
          {t('checkEmail.description')}{' '}
          <Text style={styles.boldEmail}>{email}</Text>
          {'. '}
          {t('checkEmail.verificationText')}
        </Text>
        <Text style={styles.subText}>{t('checkEmail.resendText')}</Text>
        <Button
          title={t('checkEmail.resendButton')}
          type="outline"
          buttonStyle={styles.resendButton}
          titleStyle={styles.resendButtonText}
          containerStyle={styles.resendButtonContainer}
          onPress={() => {
            navigation.navigate(AUTH_ROUTES.LANGUAGE_REGION);
          }}
          icon={<MailBoxIcon style={styles.mailBoxIcon} />}
        />
      </View>
    </SafeAreaView>
  );
};

export default CheckEmail;

const useStyles = makeStyles(theme => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    paddingBottom: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 8,
    height: 56,
  },
  backButton: {
    width: 28,
    height: 28,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.foreground,
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 24,
    paddingBottom: verticalScale(60),
  },
  heading: {
    fontSize: 22,
    fontWeight: '700',
    color: theme.colors.foreground,
    marginTop: verticalScale(32),
    marginBottom: verticalScale(12),
    textAlign: 'center',
  },
  description: {
    fontSize: 15,
    color: theme.colors.neutralGrey,
    textAlign: 'center',
    marginBottom: verticalScale(8),
  },
  boldEmail: {
    fontWeight: '700',
    color: theme.colors.foreground,
  },
  subText: {
    fontSize: 14,
    color: theme.colors.neutralGrey,
    textAlign: 'center',
    marginBottom: verticalScale(32),
  },
  resendButtonContainer: {
    width: '100%',
  },
  resendButton: {
    borderColor: theme.colors.neutralGrey,
    borderWidth: 1,
    borderRadius: 24,
    height: verticalScale(48),
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  resendButtonText: {
    color: theme.colors.foreground,
    fontWeight: '500',
    fontSize: 16,
  },
  mailBoxIcon: {
    marginRight: moderateScale(6),
  },
}));
