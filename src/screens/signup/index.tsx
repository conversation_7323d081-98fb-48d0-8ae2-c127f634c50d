import React, { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  Platform,
  SafeAreaView,
  ScrollView,
  StatusBar,
  TouchableOpacity,
  View,
} from 'react-native';
import { moderateScale, scale, verticalScale } from 'react-native-size-matters';
import { AppleIconLogo, DiscordIconLogo } from '@assets/images/svg';
import { GoogleIconLogo } from '@assets/images/svg/GoogleIconLogo';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import {
  CheckBox,
  Icon,
  Input,
  makeStyles,
  Text,
  useTheme,
} from '@rneui/themed';

import AppButton from '@/components/AppButton';
import { AuthService } from '@/services/auth';
import { AUTH_ROUTES, AuthStackNavigatorParamList } from '@/types/routes';
import { setItemToAS, STORAGE_KEYS } from '@/utils/storage';

type SignupScreenNavigationProp = NativeStackNavigationProp<
  AuthStackNavigatorParamList,
  keyof AuthStackNavigatorParamList
>;

export interface SignupFormData {
  fullName: string;
  email: string;
  password: string;
  rememberMe: boolean;
}

const SignupScreen = ({
  navigation,
}: {
  navigation: SignupScreenNavigationProp;
}) => {
  const { theme } = useTheme();
  const { t } = useTranslation();
  const styles = useStyles(theme);
  //   const appNavigation = useNavigation<StacksNavigationProp>();

  const { control, handleSubmit } = useForm<SignupFormData>({
    defaultValues: {
      fullName: '',
      email: '',
      password: '',
    },
    mode: 'onChange',
  });

  const [rememberMe, setRememberMe] = useState(false);
  const [passwordVisible, setPasswordVisible] = useState(false);

  const handleForgotPassword = () => {
    navigation.navigate(AUTH_ROUTES.FORGOT_PASSWORD);
  };

  const handleRegister = async (data: SignupFormData) => {
    const response = await AuthService.signUp(data);
    if (rememberMe) {
      setItemToAS(STORAGE_KEYS.AUTH_TOKEN, response.access_token);
    }

    navigation.navigate(AUTH_ROUTES.CHECK_EMAIL);
  };

  const handleEmailSignIn = () => {
    navigation.navigate(AUTH_ROUTES.EMAIL_SIGNIN);
  };

  const handleGoogleSignIn = () => {
    // Implement Google sign in
  };

  const handleAppleSignIn = () => {
    // Implement Apple sign in
  };

  const handleDiscordSignIn = () => {
    // Implement Discord sign in
  };

  const handleTermsPress = () => {};

  const handlePrivacyPress = () => {};

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false} style={styles.content}>
        <View style={styles.headingContainer}>
          <Text style={styles.heading}>Create Account</Text>
          <Text style={styles.subheading}>
            Please input to your account for access all features in Soleback
          </Text>
        </View>

        <View style={styles.form}>
          <Text style={styles.label}>Full Name</Text>
          <Controller
            control={control}
            name="fullName"
            render={({ field: { onChange, value } }) => (
              <Input
                placeholder="Enter your full name"
                value={value}
                onChangeText={onChange}
                autoCapitalize="words"
              />
            )}
          />
          <Text style={styles.label}>{t('emailSignIn.email', 'Email')}</Text>
          <Controller
            control={control}
            name="email"
            render={({ field: { onChange, value } }) => (
              <Input
                placeholder="<EMAIL>"
                value={value}
                onChangeText={onChange}
                autoCapitalize="none"
                keyboardType="email-address"
              />
            )}
          />

          <Text style={styles.label}>
            {t('emailSignIn.password', 'Password')}
          </Text>
          <Controller
            control={control}
            name="password"
            render={({ field: { onChange, value } }) => (
              <Input
                placeholder="Enter your password"
                value={value}
                onChangeText={onChange}
                secureTextEntry={!passwordVisible}
                rightIcon={
                  <Icon
                    name={passwordVisible ? 'eye' : 'eye-off'}
                    type="feather"
                    size={20}
                    color={theme.colors.neutralGrey}
                    onPress={() => setPasswordVisible(!passwordVisible)}
                  />
                }
              />
            )}
          />

          <View style={styles.rememberForgotContainer}>
            <CheckBox
              checked={rememberMe}
              onPress={() => setRememberMe(!rememberMe)}
              title={t('emailSignIn.rememberMe', 'Remember Me')}
              containerStyle={styles.checkboxContainer}
              textStyle={styles.checkboxText}
              checkedColor={theme.colors.primary}
              uncheckedColor={theme.colors.neutralGrey}
            />

            <TouchableOpacity onPress={handleForgotPassword}>
              <Text style={styles.forgotPassword}>
                {t('emailSignIn.forgotPassword', 'Forgot Password')}
              </Text>
            </TouchableOpacity>
          </View>

          <AppButton
            type="primary"
            size="large"
            onPress={handleSubmit(handleRegister)}
            containerStyle={styles.loginButtonContainer}
          >
            <Text style={styles.loginButtonText}>
              {t('emailSignIn.login', 'Register')}
            </Text>
          </AppButton>
        </View>

        <View>
          <Text style={styles.termsText}>
            By signing up you acknowledge and agree to soleback{' '}
            <Text style={styles.termsLink} onPress={handleTermsPress}>
              General Terms of Use
            </Text>{' '}
            and{' '}
            <Text style={styles.termsLink} onPress={handlePrivacyPress}>
              Privacy Policy
            </Text>
          </Text>
        </View>

        {/* Divider */}
        <View style={styles.dividerContainer}>
          <View style={styles.divider} />
          <Text style={styles.dividerText}>or Sign Up with</Text>
          <View style={styles.divider} />
        </View>

        {/* Social Buttons */}
        <View style={styles.socialButtonsContainer}>
          <AppButton
            type="ghost"
            size="medium"
            onPress={handleGoogleSignIn}
            containerStyle={styles.socialButton}
          >
            <GoogleIconLogo width={24} height={24} />
          </AppButton>

          <AppButton
            type="ghost"
            size="medium"
            onPress={handleAppleSignIn}
            containerStyle={styles.socialButton}
          >
            <AppleIconLogo width={24} height={24} />
          </AppButton>

          <AppButton
            type="ghost"
            size="medium"
            onPress={handleDiscordSignIn}
            containerStyle={styles.socialButton}
          >
            <DiscordIconLogo width={24} height={24} />
          </AppButton>
        </View>

        {/* Register Link */}
        <View style={styles.registerContainer}>
          <Text style={styles.registerText}>Already have an account?</Text>
          <TouchableOpacity onPress={handleEmailSignIn}>
            <Text style={styles.registerLink}>Register</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const useStyles = makeStyles(theme => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    paddingBottom: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  content: {
    flex: 1,
    paddingHorizontal: scale(24),
    marginTop: verticalScale(8),
  },
  backButton: {
    position: 'absolute',
    top: verticalScale(6),
    left: scale(16),
    width: scale(40),
    height: scale(40),
    borderRadius: scale(20),
    backgroundColor:
      theme.mode === 'dark' ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.02)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  logo: {
    width: scale(120),
    height: verticalScale(60),
    marginTop: verticalScale(26),
    marginBottom: verticalScale(20),
  },
  headingContainer: {
    width: '100%',
  },
  heading: {
    fontSize: moderateScale(34),
    fontWeight: '500',
    color: theme.colors.foreground,
  },
  subheading: {
    fontSize: moderateScale(16),
    fontWeight: '400',
    color: theme.colors.neutralGrey,
  },
  form: {
    width: '100%',
    marginTop: verticalScale(20),
  },
  label: {
    fontSize: moderateScale(16),
    fontWeight: '500',
    color: theme.colors.foreground,
    marginBottom: verticalScale(8),
  },
  rememberForgotContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: verticalScale(24),
  },
  checkboxContainer: {
    backgroundColor: 'transparent',
    borderWidth: 0,
    padding: 0,
    margin: 0,
  },
  checkboxText: {
    fontSize: moderateScale(14),
    fontWeight: 'normal',
    color: theme.colors.foreground,
    marginLeft: scale(8),
  },
  forgotPassword: {
    fontSize: moderateScale(14),
    color: theme.colors.primary,
    textDecorationLine: 'underline',
  },
  loginButtonContainer: {
    width: '100%',
    marginBottom: verticalScale(24),
  },
  loginButtonText: {
    fontSize: moderateScale(16),
    fontWeight: '600',
    color: theme.colors.secondary,
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    marginVertical: verticalScale(24),
  },
  divider: {
    flex: 1,
    height: 1,
    backgroundColor: theme.colors.grey5,
  },
  dividerText: {
    paddingHorizontal: scale(16),
    color: theme.colors.neutralGrey,
    fontSize: moderateScale(14),
  },
  socialButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
    marginBottom: verticalScale(24),
  },
  socialButton: {
    width: scale(80),
    marginHorizontal: scale(8),
  },
  registerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  registerText: {
    fontSize: moderateScale(14),
    marginRight: scale(4),
  },
  registerLink: {
    fontSize: moderateScale(14),
    color: theme.colors.primary,
    fontWeight: '500',
    textDecorationLine: 'underline',
  },
  termsText: {
    fontSize: moderateScale(12),
    textAlign: 'center',
    color: theme.colors.neutralGrey,
    lineHeight: moderateScale(18),
  },
  termsLink: {
    color: theme.colors.primary,
    fontWeight: '500',
  },
}));

export default SignupScreen;
