import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  FlatList,
  SafeAreaView,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { CountryCode, Flag } from 'react-native-country-picker-modal';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import { Button, Icon, makeStyles, useTheme } from '@rneui/themed';

import { useAppDispatch } from '@/hooks/redux';
import { fetchUserDetails } from '@/store/slices/authSlice';

const LANGUAGES = [
  { country: 'US', name: 'United States', code: 'en-US' },
  { country: 'DE', name: 'Germany', code: 'de-DE' },
  { country: 'FR', name: 'France', code: 'fr-FR' },
  { country: 'ES', name: 'Spain', code: 'es-ES' },
  { country: 'IT', name: 'Italy', code: 'it-IT' },
  { country: 'JP', name: 'Japan', code: 'ja-JP' },
  { country: 'BR', name: 'Brazil', code: 'pt-BR' },
  { country: 'CN', name: 'China', code: 'zh-CN' },
];

const LanguageRegionScreen = () => {
  const [selected, setSelected] = useState('en-US');
  const [search, setSearch] = useState('');
  const { theme } = useTheme();
  const styles = useStyles(theme);
  const { t, i18n } = useTranslation();
  const dispatch = useAppDispatch();

  const handleLanguageChange = (code: string) => {
    setSelected(code);
    // Extract language code from the full locale (e.g., 'en' from 'en-US')
    const [languageCode] = code.split('-');
    i18n.changeLanguage(languageCode);
  };

  const filteredLanguages = LANGUAGES.filter(
    l =>
      l.name.toLowerCase().includes(search.toLowerCase()) ||
      l.code.toLowerCase().includes(search.toLowerCase()),
  );

  const handleGetUserDetails = () => {
    dispatch(fetchUserDetails());
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>{t('languageRegion.title')}</Text>
        <Text style={styles.subtitle}>{t('languageRegion.subtitle')}</Text>
        <View style={styles.searchContainer}>
          <Icon name="search" type="feather" color="#98958D" size={20} />
          <TextInput
            style={styles.searchInput}
            placeholder={t('languageRegion.searchPlaceholder')}
            value={search}
            onChangeText={setSearch}
          />
        </View>
        <FlatList
          data={filteredLanguages}
          keyExtractor={item => item.code}
          showsVerticalScrollIndicator={false}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={[
                styles.item,
                selected === item.code && styles.selectedItem,
              ]}
              onPress={() => handleLanguageChange(item.code)}
              activeOpacity={0.7}
            >
              <Flag
                countryCode={item.country as CountryCode}
                flagSize={20}
                withEmoji={true}
                withFlagButton={true}
              />
              <Text style={styles.countryText}>{item.name}</Text>
              <Text style={styles.codeText}>({item.code})</Text>
              <View style={styles.radioOuter}>
                {selected === item.code && <View style={styles.radioInner} />}
              </View>
            </TouchableOpacity>
          )}
          style={styles.list}
        />
        <Button
          title={t('languageRegion.next')}
          buttonStyle={styles.nextButton}
          titleStyle={styles.nextButtonText}
          containerStyle={styles.nextButtonContainer}
          onPress={handleGetUserDetails}
        />
      </View>
    </SafeAreaView>
  );
};

const useStyles = makeStyles(theme => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  content: {
    flex: 1,
    padding: moderateScale(20),
  },
  title: {
    fontSize: 28,
    fontFamily: 'Inter',
    fontWeight: '600',
    marginBottom: verticalScale(8),
    color: theme.colors.foreground,
  },
  subtitle: {
    fontSize: 14,
    color: theme.colors.grey1,
    marginBottom: verticalScale(16),
    fontFamily: 'Inter',
    fontWeight: '400',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.searchBg,
    borderRadius: 50,
    paddingHorizontal: moderateScale(12),
    marginBottom: verticalScale(12),
    height: verticalScale(50),
    borderWidth: 1,
    borderColor: theme.colors.grey3,
  },
  searchInput: {
    flex: 1,
    marginLeft: moderateScale(8),
    fontSize: 15,
    color: theme.colors.foreground,
  },
  list: { flex: 1 },
  item: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: moderateScale(14),
    borderRadius: 24,
    marginBottom: verticalScale(8),
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: 'transparent',
  },
  selectedItem: {
    backgroundColor: '#FFF8E1',
    borderColor: theme.colors.primary,
  },
  flag: { marginRight: moderateScale(12) },
  countryText: {
    fontSize: 16,
    color: theme.colors.foreground,
    flex: 1,
    textAlign: 'left',
  },
  codeText: {
    textAlign: 'left',
    flex: 1,
    fontSize: 14,
    color: theme.colors.grey0,
    marginRight: moderateScale(12),
  },
  radioOuter: {
    width: moderateScale(22),
    height: verticalScale(22),
    borderRadius: 11,
    borderWidth: 2,
    borderColor: theme.colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
  },
  radioInner: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: theme.colors.primary,
  },
  nextButtonContainer: {
    marginTop: verticalScale(16),
    width: '100%',
  },
  nextButton: {
    borderRadius: 24,
    height: verticalScale(48),
    backgroundColor: theme.colors.primary,
  },
  nextButtonText: {
    color: theme.colors.foreground,
    fontWeight: '500',
    fontSize: 16,
    fontFamily: 'Inter',
  },
}));

export default LanguageRegionScreen;
