import React from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/Feather';
import { makeStyles, useTheme } from '@rneui/themed';

import { FilterIconOutline } from '@/constants/svgs';

export interface Transaction {
  id: string;
  storeName: string;
  storeLogo: string;
  orderNumber: string;
  visitDate: string;
  visitTime: string;
  status: 'pending' | 'approved' | 'withdrawable';
  cashbackPercentage: number;
}

interface TransactionHistoryCardProps {
  onTransactionPress?: (transaction: Transaction) => void;
  onFilterPress?: () => void;
}

const dummyTransactions: Transaction[] = [
  {
    id: '1',
    storeName: 'Falcon Flagship Store',
    storeLogo: 'falcon',
    orderNumber: '90312',
    visitDate: '12/12/2024',
    visitTime: '9:32 PM',
    status: 'pending',
    cashbackPercentage: 10,
  },
  {
    id: '2',
    storeName: 'Puma Poland',
    storeLogo: 'puma',
    orderNumber: '90312',
    visitDate: '12/12/2024',
    visitTime: '9:32 PM',
    status: 'pending',
    cashbackPercentage: 8,
  },
  {
    id: '3',
    storeName: 'Adidas UK',
    storeLogo: 'adidas',
    orderNumber: '90312',
    visitDate: '12/12/2024',
    visitTime: '9:32 PM',
    status: 'approved',
    cashbackPercentage: 5,
  },
  {
    id: '4',
    storeName: 'StockX',
    storeLogo: 'stockx',
    orderNumber: '90312',
    visitDate: '12/12/2024',
    visitTime: '9:32 PM',
    status: 'withdrawable',
    cashbackPercentage: 8,
  },
  {
    id: '5',
    storeName: 'Enix Flagship Store',
    storeLogo: 'enix',
    orderNumber: '90312',
    visitDate: '12/12/2024',
    visitTime: '9:32 PM',
    status: 'approved',
    cashbackPercentage: 10,
  },
];

const getStoreLogo = (storeLogo: string) => {
  switch (storeLogo) {
    case 'falcon':
      return '🦅';
    case 'puma':
      return '🐆';
    case 'adidas':
      return '🏔️';
    case 'stockx':
      return '📦';
    case 'enix':
      return '🔷';
    default:
      return '🏪';
  }
};

const getStatusConfig = (status: Transaction['status']) => {
  switch (status) {
    case 'pending':
      return {
        color: '#FFB800',
        backgroundColor: '#FFF8E1',
        borderColor: '#FFB800',
        icon: 'clock',
        text: 'Pending',
      };
    case 'approved':
      return {
        color: '#4CAF50',
        backgroundColor: '#E8F5E8',
        borderColor: '#4CAF50',
        icon: 'check-circle',
        text: 'Approved',
      };
    case 'withdrawable':
      return {
        color: '#9C27B0',
        backgroundColor: '#F3E5F5',
        borderColor: '#9C27B0',
        icon: 'credit-card',
        text: 'Withdrawable',
      };
  }
};

const TransactionHistoryCard: React.FC<TransactionHistoryCardProps> = ({
  onTransactionPress,
  onFilterPress,
}) => {
  const { theme } = useTheme();
  const styles = useStyles(theme);

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Transaction History</Text>
        <TouchableOpacity style={styles.filterButton} onPress={onFilterPress}>
          <FilterIconOutline color={theme.colors.black} />
        </TouchableOpacity>
      </View>

      {/* Transaction List */}
      <View style={styles.transactionList}>
        {dummyTransactions.map((transaction, index) => {
          const statusConfig = getStatusConfig(transaction.status);
          const isLast = index === dummyTransactions.length - 1;

          return (
            <React.Fragment key={transaction.id}>
              <TouchableOpacity
                style={styles.transactionCard}
                onPress={() => onTransactionPress?.(transaction)}
              >
                {/* Store Logo */}
                <View style={styles.logoContainer}>
                  <View style={styles.logoCircle}>
                    <Text style={styles.logoText}>
                      {getStoreLogo(transaction.storeLogo)}
                    </Text>
                  </View>
                </View>

                {/* Transaction Details */}
                <View style={styles.detailsContainer}>
                  <View style={styles.titleRow}>
                    <Text style={styles.storeName}>
                      {transaction.storeName}
                    </Text>
                  </View>
                  <Text style={styles.orderDetails}>
                    Order #{transaction.orderNumber} • Store Visit{' '}
                    {transaction.visitDate}; {transaction.visitTime}
                  </Text>

                  {/* Status and Cashback */}
                  <View style={styles.statusContainer}>
                    <View
                      style={[
                        styles.statusBadge,
                        {
                          backgroundColor: statusConfig.backgroundColor,
                          borderColor: statusConfig.borderColor,
                        },
                      ]}
                    >
                      <Icon
                        name={statusConfig.icon}
                        size={moderateScale(10)}
                        color={statusConfig.color}
                      />
                      <Text
                        style={[
                          styles.statusText,
                          { color: statusConfig.color },
                        ]}
                      >
                        {statusConfig.text}
                      </Text>
                    </View>
                    <View style={styles.cashbackBadge}>
                      <Text style={styles.cashbackText}>
                        {transaction.cashbackPercentage}% Cashback Earned
                      </Text>
                    </View>
                  </View>
                </View>

                {/* Arrow Icon */}
                <View style={styles.arrowContainer}>
                  <Icon
                    name="chevron-right"
                    size={moderateScale(14)}
                    color={theme.colors.grey2}
                  />
                </View>
              </TouchableOpacity>

              {/* Divider */}
              {!isLast && <View style={styles.divider} />}
            </React.Fragment>
          );
        })}
      </View>
    </View>
  );
};

const useStyles = makeStyles(theme => ({
  container: {
    backgroundColor: theme.colors.white,
    borderRadius: moderateScale(12),
    borderWidth: moderateScale(1),
    borderColor: theme.colors.grey3,
    marginHorizontal: moderateScale(20),
    marginTop: verticalScale(16),
    padding: moderateScale(16),
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: verticalScale(16),
  },
  headerTitle: {
    fontSize: moderateScale(18),
    fontWeight: 'bold',
    color: theme.colors.black,
  },
  filterButton: {
    width: moderateScale(32),
    height: moderateScale(32),
    borderRadius: moderateScale(16),
    justifyContent: 'center',
    alignItems: 'center',
  },
  transactionList: {
    gap: verticalScale(4),
    paddingHorizontal: moderateScale(2),
  },
  transactionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: verticalScale(4),
    backgroundColor: theme.colors.white,
  },
  lastCard: {
    marginBottom: 0,
  },
  logoContainer: {
    marginRight: moderateScale(12),
    alignSelf: 'flex-start',
    marginTop: verticalScale(2),
  },
  logoCircle: {
    width: moderateScale(40),
    height: moderateScale(40),
    borderRadius: moderateScale(20),
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoText: {
    fontSize: moderateScale(16),
  },
  detailsContainer: {
    flex: 1,
    marginRight: moderateScale(12),
  },
  titleRow: {
    marginBottom: verticalScale(4),
  },
  storeName: {
    fontSize: moderateScale(14),
    fontWeight: '600',
    color: theme.colors.black,
  },
  orderDetails: {
    fontSize: moderateScale(12),
    color: theme.colors.grey2,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: verticalScale(8),
    gap: moderateScale(8),
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: moderateScale(6),
    paddingVertical: verticalScale(3),
    borderRadius: moderateScale(4),
    borderWidth: moderateScale(1),
    gap: moderateScale(3),
  },
  statusText: {
    fontSize: moderateScale(9),
    fontWeight: '600',
  },
  cashbackBadge: {
    backgroundColor: theme.colors.white,
    borderWidth: moderateScale(1),
    borderColor: theme.colors.grey3,
    paddingHorizontal: moderateScale(6),
    paddingVertical: verticalScale(3),
    borderRadius: moderateScale(4),
  },
  cashbackText: {
    fontSize: moderateScale(9),
    color: theme.colors.black,
    fontWeight: '500',
  },
  arrowContainer: {
    marginLeft: moderateScale(4),
    alignSelf: 'flex-start',
    marginTop: verticalScale(2),
  },
  divider: {
    height: moderateScale(1),
    backgroundColor: theme.colors.grey3,
    marginVertical: verticalScale(8),

    marginHorizontal: moderateScale(-20),
  },
}));

export default TransactionHistoryCard;
