import React from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/Feather';
import { makeStyles, useTheme } from '@rneui/themed';

import { CrownIcon } from '@/constants/svgs';

interface BalanceCardProps {
  onWithdrawPress?: () => void;
  onPendingBalancePress?: () => void;
}

const BalanceCard: React.FC<BalanceCardProps> = ({
  onWithdrawPress,
  onPendingBalancePress,
}) => {
  const { theme } = useTheme();
  const styles = useStyles(theme);

  return (
    <View style={styles.container}>
      {/* Diamond Tier Card */}
      <View style={styles.tierCard}>
        <View style={styles.tierInfo}>
          <View style={styles.crownContainer}>
            <CrownIcon color={theme.colors.white} />
          </View>
          <View style={styles.tierTextContainer}>
            <View style={styles.tierRow}>
              <Text style={styles.tierText}>Dimond T3</Text>
              <Text style={styles.progressText}>1,940/3000</Text>
            </View>
            <View style={styles.progressBar}>
              <View style={styles.progressFill} />
            </View>
          </View>
        </View>
      </View>

      {/* Balance Section */}
      <View style={styles.balanceSection}>
        <View style={styles.balanceInfo}>
          <Text style={styles.balanceLabel}>Withdrawable Balance</Text>
          <Text style={styles.balanceAmount}>$30.75</Text>
        </View>
        <TouchableOpacity
          style={styles.withdrawButton}
          onPress={onWithdrawPress}
        >
          <Icon
            name="arrow-up-right"
            size={moderateScale(16)}
            color={theme.colors.grey2}
          />
          <Text style={styles.withdrawText}>Withdraw</Text>
        </TouchableOpacity>
      </View>

      {/* Pending Balance */}
      <View style={styles.pendingSection}>
        <Text style={styles.pendingText}>
          You've $29.3 pending balance,{' '}
          <Text style={styles.pendingDate} onPress={onPendingBalancePress}>
            available on 5th Dec
          </Text>
        </Text>
      </View>
    </View>
  );
};

const useStyles = makeStyles(theme => ({
  container: {
    backgroundColor: theme.colors.white,
    borderRadius: moderateScale(12),
    borderWidth: moderateScale(1),
    borderColor: theme.colors.grey3,
    marginHorizontal: moderateScale(20),
    marginTop: verticalScale(10),
    paddingHorizontal: moderateScale(6),
    paddingVertical: verticalScale(6),
    gap: verticalScale(16),
  },
  tierCard: {
    backgroundColor: `${theme.colors.primary}20`,
    borderRadius: moderateScale(8),
    paddingHorizontal: moderateScale(8),
    paddingVertical: verticalScale(16),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  tierInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  crownContainer: {
    width: moderateScale(32),
    height: moderateScale(32),
    borderRadius: moderateScale(16),
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: moderateScale(12),
  },
  tierTextContainer: {
    flex: 1,
  },
  tierRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: verticalScale(8),
  },
  tierText: {
    fontSize: moderateScale(14),
    fontWeight: '600',
    color: theme.colors.black,
  },
  progressBar: {
    height: verticalScale(8),
    backgroundColor: theme.colors.white,
    borderRadius: moderateScale(2),
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    width: '65%',
    backgroundColor: theme.colors.primary,
    borderRadius: moderateScale(2),
  },
  progressText: {
    fontSize: moderateScale(14),
    fontWeight: '600',
    color: theme.colors.black,
  },
  balanceSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: moderateScale(6),
  },
  balanceInfo: {
    flex: 1,
  },
  balanceLabel: {
    fontSize: moderateScale(14),
    color: theme.colors.grey2,
    marginBottom: verticalScale(4),
  },
  balanceAmount: {
    fontSize: moderateScale(24),
    fontWeight: 'bold',
    color: theme.colors.black,
  },
  withdrawButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.white,
    borderWidth: moderateScale(1),
    borderColor: theme.colors.grey3,
    borderRadius: moderateScale(8),
    paddingHorizontal: moderateScale(12),
    paddingVertical: verticalScale(8),
    gap: moderateScale(4),
  },
  withdrawText: {
    fontSize: moderateScale(14),
    fontWeight: '500',
    color: theme.colors.black,
  },
  pendingSection: {
    borderTopWidth: moderateScale(1),
    borderTopColor: theme.colors.grey3,
    paddingTop: verticalScale(8),
    paddingHorizontal: moderateScale(6),
  },
  pendingText: {
    fontSize: moderateScale(12),
    color: theme.colors.grey2,
  },
  pendingDate: {
    textDecorationLine: 'underline',
    color: theme.colors.primary,
  },
}));

export default BalanceCard;
