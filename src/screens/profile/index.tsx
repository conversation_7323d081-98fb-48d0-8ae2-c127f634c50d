import React, { useEffect } from 'react';
import { Image, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { SafeAreaView } from 'react-native-safe-area-context';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import { useNavigation } from '@react-navigation/native';
import { makeStyles, useTheme } from '@rneui/themed';

import { CameraIcon, SettingsIcon } from '@/constants/svgs';
import { useAppDispatch, useAppSelector } from '@/hooks/redux';
import { fetchUserProfile } from '@/store/slices/homeSlice';
import { UserProfile } from '@/types/home';
import { APP_ROUTES } from '@/types/routes';

import type { Transaction } from './components/TransactionHistoryCard';
import { BalanceCard, TransactionHistoryCard } from './components';

const getUserInitials = (user: UserProfile | null) => {
  if (user?.first_name && user?.last_name) {
    return `${user.first_name.charAt(0)}${user.last_name.charAt(0)}`.toUpperCase();
  } else if (user?.first_name) {
    return user.first_name.charAt(0).toUpperCase();
  } else if (user?.last_name) {
    return user.last_name.charAt(0).toUpperCase();
  }

  return null;
};

const getUserFullName = (user: UserProfile | null) => {
  if (user?.first_name && user?.last_name) {
    return `${user.first_name} ${user.last_name}`;
  } else if (user?.first_name) {
    return user.first_name;
  } else if (user?.last_name) {
    return user.last_name;
  }

  return 'User Name';
};

const ProfileScreen = () => {
  const { theme } = useTheme();
  const styles = useStyles(theme);
  const navigation = useNavigation();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector(state => state.home);

  useEffect(() => {
    dispatch(fetchUserProfile());
  }, [dispatch]);

  const handleWithdrawPress = () => {};

  const handlePendingBalancePress = () => {};

  const handleTransactionPress = (_transaction: Transaction) => {};

  const handleFilterPress = () => {};

  return (
    <View style={styles.container}>
      <SafeAreaView
        edges={['top', 'left', 'right']}
        style={styles.headerSafeArea}
      >
        <LinearGradient
          colors={[theme.colors.foreground, theme.colors.brownGradient]}
        >
          <View style={styles.headerContent}>
            <View style={styles.headerRow}>
              <Text style={styles.headerTitle}>Profile</Text>
              <TouchableOpacity
                style={styles.settingsBtn}
                onPress={() => {
                  // @ts-ignore - Navigation will be handled by the stack navigator
                  navigation.navigate(APP_ROUTES.PROFILE_SETTINGS);
                }}
              >
                <SettingsIcon color={theme.colors.white} />
              </TouchableOpacity>
            </View>
          </View>
        </LinearGradient>
      </SafeAreaView>

      <View style={styles.profileSection}>
        <View style={styles.avatarContainer}>
          <View style={styles.avatar}>
            {user?.avatar_url ? (
              <Image
                source={{ uri: user.avatar_url }}
                style={styles.avatarImage}
              />
            ) : (
              <View style={styles.avatarPlaceholder}>
                <Text style={styles.avatarText}>
                  {getUserInitials(user) || 'U'}
                </Text>
              </View>
            )}
            <View style={styles.cameraIcon}>
              <CameraIcon color={theme.colors.black} />
            </View>
          </View>
        </View>

        <View style={styles.userInfo}>
          <Text style={styles.userName}>{getUserFullName(user)}</Text>
          <Text style={styles.userEmail}>
            {user?.email || '<EMAIL>'}
          </Text>
        </View>
      </View>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <BalanceCard
          onWithdrawPress={handleWithdrawPress}
          onPendingBalancePress={handlePendingBalancePress}
        />
        <TransactionHistoryCard
          onTransactionPress={handleTransactionPress}
          onFilterPress={handleFilterPress}
        />
      </ScrollView>
    </View>
  );
};

export default ProfileScreen;

const useStyles = makeStyles(theme => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  headerSafeArea: {
    backgroundColor: theme.colors.foreground,
  },
  headerContent: {
    paddingBottom: verticalScale(30),
    paddingTop: verticalScale(10),
    paddingHorizontal: moderateScale(16),
    gap: verticalScale(10),
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: verticalScale(12),
  },
  headerTitle: {
    color: theme.colors.white,
    fontSize: moderateScale(20),
    fontWeight: '500',
  },
  settingsBtn: {
    borderWidth: moderateScale(1),
    borderColor: `${theme.colors.white}80`,
    borderRadius: moderateScale(100),
    padding: moderateScale(8),
  },
  profileSection: {
    alignItems: 'center',
    marginTop: verticalScale(-30),
    marginBottom: verticalScale(10),
  },
  avatarContainer: {
    marginBottom: verticalScale(20),
  },
  avatar: {
    width: moderateScale(120),
    height: moderateScale(120),
    borderRadius: moderateScale(60),
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',

    shadowColor: theme.colors.black,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
    zIndex: 10,
  },
  avatarImage: {
    width: '100%',
    height: '100%',
    borderRadius: moderateScale(60),
  },
  avatarPlaceholder: {
    width: '100%',
    height: '100%',
    borderRadius: moderateScale(60),
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: moderateScale(48),
    fontWeight: 'bold',
    color: theme.colors.white,
  },
  cameraIcon: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: moderateScale(40),
    height: moderateScale(40),
    borderRadius: moderateScale(20),
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: theme.colors.white,
  },
  userInfo: {
    alignItems: 'center',
    paddingHorizontal: moderateScale(20),
  },
  userName: {
    fontSize: moderateScale(24),
    fontWeight: '500',
    color: theme.colors.foreground,
    marginBottom: verticalScale(8),
    textAlign: 'center',
  },
  userEmail: {
    fontSize: moderateScale(16),
    color: theme.colors.grey2,
    fontWeight: '400',
    marginBottom: verticalScale(4),
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: verticalScale(20),
  },
}));
