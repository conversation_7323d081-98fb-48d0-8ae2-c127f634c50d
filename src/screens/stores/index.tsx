import React, { useCallback, useEffect, useState } from 'react';
import {
  ActivityIndicator,
  FlatList,
  Image,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { SafeAreaView } from 'react-native-safe-area-context';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/Feather';
import { useDispatch, useSelector } from 'react-redux';
import {
  type RouteProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { makeStyles, useTheme } from '@rneui/themed';

import { AppDispatch, RootState } from '@/store';
import {
  fetchCashbackStores,
  fetchFeaturedStores,
} from '@/store/slices/cashbackSlice';
import { Store } from '@/types/cashback';
import { APP_ROUTES, AppStackNavigatorParamList } from '@/types/routes';

type AllStoresRouteProp = RouteProp<
  AppStackNavigatorParamList,
  APP_ROUTES.ALL_STORES
>;

const AllStoresScreen = () => {
  const { theme } = useTheme();
  const styles = useStyles(theme);
  const navigation =
    useNavigation<NativeStackNavigationProp<AppStackNavigatorParamList>>();
  const route = useRoute<AllStoresRouteProp>();
  const { featured } = route.params || {};

  const dispatch = useDispatch<AppDispatch>();
  const { stores, loading, error } = useSelector(
    (state: RootState) => state.cashback,
  );
  const { featuredStores, featuredStoresLoading, featuredStoresError } =
    useSelector((state: RootState) => state.cashback);

  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  // Use appropriate data based on featured parameter
  const currentStores = featured ? featuredStores : stores;
  const currentLoading = featured ? featuredStoresLoading : loading;
  const currentError = featured ? featuredStoresError : error;

  useEffect(() => {
    if (featured) {
      dispatch(fetchFeaturedStores({ page: 1, perPage: 10 }));
    } else {
      dispatch(fetchCashbackStores({ page: 1, perPage: 10 }));
    }
  }, [dispatch, featured]);

  const loadMoreStores = useCallback(async () => {
    if (isLoadingMore || !hasMore) return;

    setIsLoadingMore(true);
    try {
      // Fetch more stores with pagination
      const result = await dispatch(
        featured
          ? fetchFeaturedStores({ page: page + 1, perPage: 10 })
          : fetchCashbackStores({ page: page + 1, perPage: 10 }),
      );

      // Check if result exists and has payload
      if (
        result.payload &&
        Array.isArray(result.payload) &&
        result.payload.length > 0
      ) {
        setPage(page + 1);
      } else {
        setHasMore(false);
      }
    } catch (err) {
      setHasMore(false);
    } finally {
      setIsLoadingMore(false);
    }
  }, [dispatch, page, isLoadingMore, hasMore, featured]);

  const renderItem = ({ item }: { item: Store }) => (
    <TouchableOpacity
      style={styles.card}
      activeOpacity={0.9}
      onPress={() =>
        navigation.navigate(APP_ROUTES.STORE_DETAIL, { storeId: item.id })
      }
    >
      <View style={styles.row}>
        <Image source={{ uri: item.logo }} style={styles.logo} />
        <View style={styles.info}>
          <Text style={styles.name}>{item.name}</Text>
          <Text style={styles.desc} numberOfLines={2}>
            {item.description}
          </Text>
          {!item.special_deal && (
            <View style={styles.discountBadge}>
              <Text style={styles.discountText}>
                {item.special_deal_percent
                  ? `${item.special_deal_percent}% In Store Discount`
                  : '10% In store discount'}
              </Text>
            </View>
          )}
        </View>
        <Icon
          name="chevron-right"
          size={moderateScale(20)}
          color={theme.colors.foreground}
          style={styles.arrowIcon}
        />
      </View>
    </TouchableOpacity>
  );

  if (currentError) {
    return (
      <View style={styles.container}>
        <SafeAreaView
          edges={['top', 'left', 'right']}
          style={styles.headerSafeArea}
        >
          <LinearGradient
            colors={[theme.colors.foreground, theme.colors.brownGradient]}
          >
            <View style={styles.headerContent}>
              <View style={styles.headerRow}>
                <TouchableOpacity
                  style={styles.backButton}
                  onPress={() => navigation.goBack()}
                >
                  <Icon
                    name="arrow-left"
                    size={moderateScale(24)}
                    color={theme.colors.white}
                  />
                </TouchableOpacity>
                <Text style={styles.headerTitle}>All Stores</Text>
                <View style={styles.placeholder} />
              </View>
            </View>
          </LinearGradient>
        </SafeAreaView>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{currentError}</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <SafeAreaView
        edges={['top', 'left', 'right']}
        style={styles.headerSafeArea}
      >
        <LinearGradient
          colors={[theme.colors.foreground, theme.colors.brownGradient]}
        >
          <View style={styles.headerContent}>
            <View style={styles.headerRow}>
              <TouchableOpacity
                style={styles.backButton}
                onPress={() => navigation.goBack()}
              >
                <Icon name="arrow-left" size={24} color={theme.colors.white} />
              </TouchableOpacity>
              <Text style={styles.headerTitle}>
                {featured ? 'Top Sneaker Stores' : 'Popular Stores'}
              </Text>
              <View style={styles.placeholder} />
            </View>
          </View>
        </LinearGradient>
      </SafeAreaView>

      <FlatList
        data={currentStores}
        renderItem={renderItem}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        onEndReached={loadMoreStores}
        onEndReachedThreshold={0.1}
        ListFooterComponent={
          isLoadingMore ? (
            <View style={styles.loadingMoreContainer}>
              <ActivityIndicator
                size="small"
                color={theme.colors.primary}
                style={styles.loadingMoreSpinner}
              />
            </View>
          ) : null
        }
        ListEmptyComponent={
          currentLoading ? (
            <View style={styles.emptyContainer}>
              <ActivityIndicator
                size="large"
                color={theme.colors.primary}
                style={styles.loadingSpinner}
              />
            </View>
          ) : (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>No stores available</Text>
            </View>
          )
        }
      />
    </View>
  );
};

const useStyles = makeStyles(theme => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  headerSafeArea: {
    backgroundColor: theme.colors.foreground,
  },
  headerContent: {
    paddingHorizontal: moderateScale(16),
    paddingVertical: moderateScale(12),
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  backButton: {
    padding: moderateScale(8),
  },
  headerTitle: {
    fontFamily: 'Inter',
    fontWeight: '600',
    fontSize: moderateScale(18),
    color: theme.colors.white,
  },
  placeholder: {
    flex: 1,
  },
  listContainer: {
    paddingBottom: 20,
  },
  card: {
    backgroundColor: theme.colors.background,
    borderRadius: moderateScale(4),
    marginHorizontal: moderateScale(16),
    marginVertical: verticalScale(8),
    padding: moderateScale(18),
    boxShadow: `0px 11.82px 22.61px 0px ${theme.colors.grey3}`,
    elevation: 2,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  logo: {
    width: moderateScale(44),
    height: verticalScale(44),
    borderRadius: moderateScale(8),
    marginRight: moderateScale(14),
    backgroundColor: theme.colors.neutralGrey,
    marginTop: verticalScale(2),
  },
  info: {
    flex: 1,
    flexDirection: 'column',
  },
  name: {
    fontWeight: '700',
    fontSize: moderateScale(16),
    color: theme.colors.foreground,
    marginBottom: verticalScale(2),
  },
  desc: {
    fontSize: moderateScale(13),
    color: theme.colors.neutralGrey,
    marginBottom: verticalScale(8),
    fontWeight: '400',
  },
  arrowIcon: {
    marginLeft: moderateScale(10),
    marginTop: verticalScale(6),
  },
  discountBadge: {
    backgroundColor: theme.colors.cashbackBg,
    borderWidth: 1,
    borderColor: theme.colors.cashbackBorder,
    borderRadius: moderateScale(6),
    paddingHorizontal: moderateScale(12),
    paddingVertical: verticalScale(4),
    alignSelf: 'flex-start',
    marginTop: 0,
  },
  discountText: {
    color: theme.colors.cashbackText,
    fontWeight: '600',
    fontSize: moderateScale(13),
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: moderateScale(20),
  },
  errorText: {
    color: 'red',
    fontSize: moderateScale(16),
    textAlign: 'center',
  },
  loadingMoreContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: verticalScale(20),
  },
  loadingMoreSpinner: {
    marginRight: moderateScale(10),
  },
  loadingSpinner: {
    marginBottom: verticalScale(10),
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: verticalScale(40),
  },
  emptyText: {
    fontSize: moderateScale(16),
    color: theme.colors.neutralGrey,
    textAlign: 'center',
  },
}));

export default AllStoresScreen;
