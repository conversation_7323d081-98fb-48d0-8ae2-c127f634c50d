import React from 'react';
import { useTranslation } from 'react-i18next';
import { Text, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import { makeStyles } from '@rneui/themed';

import AppButton from '@/components/AppButton';
import LanguageSwitch from '@/components/LanguageSwitch';
import ThemeModeSwitch from '@/components/ThemeModeSwitch';
import { useAppDispatch } from '@/hooks/redux';
import { clearUser } from '@/store/slices/authSlice';
import { removeItemFromAS, STORAGE_KEYS } from '@/utils/storage';

const Dashboard = () => {
  const { t } = useTranslation();
  const styles = useStyles();
  const dispatch = useAppDispatch();

  const handleLogout = async () => {
    await removeItemFromAS(STORAGE_KEYS.AUTH_TOKEN);
    dispatch(clearUser());
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <LanguageSwitch />
        <Text style={styles.title}>{t('login.title')}</Text>
        <View style={styles.switchContainer}>
          <ThemeModeSwitch />
        </View>
        <AppButton title="Logout" onPress={handleLogout} />
      </View>
    </SafeAreaView>
  );
};

export default Dashboard;

const useStyles = makeStyles(theme => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: moderateScale(24),
    fontWeight: 'bold',
    color: theme.colors.foreground,
  },
  switchContainer: {
    marginTop: verticalScale(20),
  },
}));
