import React, { useState } from 'react';
import { Controller, useForm, useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { SafeAreaView, View } from 'react-native';
import { moderateScale, scale, verticalScale } from 'react-native-size-matters';
import { ResetPasswordIcon } from '@assets/images/svg';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Icon, Input, makeStyles, Text, useTheme } from '@rneui/themed';

import AppButton from '@/components/AppButton';
import { AUTH_ROUTES, AuthStackNavigatorParamList } from '@/types/routes';

type ResetPasswordScreenNavigationProp = NativeStackNavigationProp<
  AuthStackNavigatorParamList,
  keyof AuthStackNavigatorParamList
>;

const ResetPasswordScreen = ({
  navigation,
}: {
  navigation: ResetPasswordScreenNavigationProp;
}) => {
  const { theme } = useTheme();
  const styles = useStyles(theme);
  const { t } = useTranslation();

  const { control } = useForm({
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
    mode: 'onChange',
  });

  const password = useWatch({ control, name: 'password' });

  const [passwordVisible, setPasswordVisible] = useState(false);
  const [confirmPasswordVisible, setConfirmPasswordVisible] = useState(false);

  const handleSendResetInstructions = () => {
    console.log('send reset instructions');
  };

  const handleGoBack = () => {
    navigation.navigate(AUTH_ROUTES.EMAIL_SIGNIN);
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <View style={styles.headingContainer}>
          <ResetPasswordIcon style={styles.icon} />
          <Text style={styles.heading}>Reset Password</Text>
          <Text style={styles.subheading}>
            Create a new password for your account by filling out the form below
          </Text>
        </View>
        <View style={styles.formContainer}>
          <View style={styles.form}>
            <Text style={styles.label}>
              {t('emailSignIn.password', 'Password')}
            </Text>
            <Controller
              control={control}
              name="password"
              render={({ field: { onChange, value } }) => (
                <Input
                  placeholder="Enter your password"
                  value={value}
                  onChangeText={onChange}
                  secureTextEntry={!passwordVisible}
                  rightIcon={
                    <Icon
                      name={passwordVisible ? 'eye' : 'eye-off'}
                      type="feather"
                      size={20}
                      color={theme.colors.neutralGrey}
                      onPress={() => setPasswordVisible(!passwordVisible)}
                    />
                  }
                />
              )}
            />
            <Text style={styles.label}>Confirm Password</Text>
            <Controller
              control={control}
              name="confirmPassword"
              render={({ field: { onChange, value } }) => (
                <Input
                  placeholder="Confirm your password"
                  value={value}
                  onChangeText={onChange}
                  secureTextEntry={!confirmPasswordVisible}
                  rightIcon={
                    <Icon
                      name={confirmPasswordVisible ? 'eye' : 'eye-off'}
                      type="feather"
                      size={20}
                      color={theme.colors.neutralGrey}
                      onPress={() =>
                        setConfirmPasswordVisible(!confirmPasswordVisible)
                      }
                    />
                  }
                />
              )}
            />
            {/* Password Requirements Component (no name/email check) */}
            <PasswordRequirements password={password} />
          </View>
          <View style={styles.buttonContainer}>
            <View style={styles.buttonColumn}>
              <AppButton
                type="primary"
                size="large"
                onPress={handleSendResetInstructions}
                containerStyle={styles.loginButtonContainer}
              >
                <Text style={styles.loginButtonText}>Confirm Password</Text>
              </AppButton>
              <AppButton
                type="ghost"
                size="large"
                onPress={handleGoBack}
                containerStyle={styles.loginButtonContainer}
              >
                <Icon
                  name="arrowleft"
                  type="antdesign"
                  style={styles.arrowLeft}
                />
                <Text style={styles.loginButtonText}>Go Back</Text>
              </AppButton>
            </View>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

// PasswordRequirements for reset password (no name/email check)
interface PasswordRequirementsProps {
  password: string;
}

const PasswordRequirements = ({ password }: PasswordRequirementsProps) => {
  const { theme } = useTheme();
  const styles = useStyles(theme);
  const requirements = [
    {
      label: 'Must be at least 8 characters',
      valid: password && password.length >= 8,
    },
    {
      label: 'Must have at least a symbol or number',
      valid:
        password && /[0-9!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]+/.test(password),
    },
  ];

  return (
    <View style={styles.requirementsContainer}>
      {requirements.map((req, idx) => (
        <View key={idx} style={styles.requirementRow}>
          <View style={styles.requirementDot} />
          <Text style={styles.requirementText}>{req.label}</Text>
        </View>
      ))}
    </View>
  );
};

const useStyles = makeStyles(theme => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  content: {
    flex: 1,
    paddingHorizontal: scale(24),
    alignItems: 'center',
    marginTop: verticalScale(18),
  },
  backButton: {
    position: 'absolute',
    top: verticalScale(6),
    left: scale(16),
    width: scale(40),
    height: scale(40),
    borderRadius: scale(20),
    backgroundColor:
      theme.mode === 'dark' ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.02)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  icon: {
    marginHorizontal: 'auto',
    marginVertical: verticalScale(10),
  },
  headingContainer: {
    width: '100%',
  },
  heading: {
    fontSize: moderateScale(34),
    fontWeight: '600',
    color: theme.colors.foreground,
  },
  subheading: {
    fontSize: moderateScale(16),
    color: theme.colors.neutralGrey,
  },
  form: {
    width: '100%',
    marginTop: verticalScale(20),
  },
  label: {
    fontSize: moderateScale(16),
    fontWeight: '500',
    color: theme.colors.foreground,
    marginBottom: verticalScale(8),
  },
  rememberForgotContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: verticalScale(24),
  },
  checkboxContainer: {
    backgroundColor: 'transparent',
    borderWidth: 0,
    padding: 0,
    margin: 0,
  },
  checkboxText: {
    fontSize: moderateScale(14),
    fontWeight: 'normal',
    color: theme.colors.foreground,
    marginLeft: scale(8),
  },
  forgotPassword: {
    fontSize: moderateScale(14),
    color: theme.colors.primary,
    textDecorationLine: 'underline',
  },
  loginButtonContainer: {
    width: '100%',
  },
  loginButtonText: {
    fontSize: moderateScale(16),
    fontWeight: '600',
    color: theme.colors.secondary,
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    marginBottom: verticalScale(24),
  },
  divider: {
    flex: 1,
    height: 1,
    backgroundColor: theme.colors.grey5,
  },
  dividerText: {
    paddingHorizontal: scale(16),
    color: theme.colors.neutralGrey,
    fontSize: moderateScale(14),
  },
  socialButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
    marginBottom: verticalScale(40),
  },
  socialButton: {
    width: scale(80),
    marginHorizontal: scale(8),
  },
  registerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  registerText: {
    fontSize: moderateScale(14),
    marginRight: scale(4),
  },
  registerLink: {
    fontSize: moderateScale(14),
    color: theme.colors.primary,
    fontWeight: '500',
    textDecorationLine: 'underline',
  },
  termsText: {
    fontSize: moderateScale(12),
    textAlign: 'center',
    color: theme.colors.neutralGrey,
    lineHeight: moderateScale(18),
  },
  supportLink: {
    fontWeight: '500',
  },
  arrowLeft: {
    marginRight: scale(4),
  },
  buttonContainer: {
    width: '100%',
    marginBottom: verticalScale(24),
  },
  buttonColumn: {
    gap: verticalScale(12),
    marginBottom: verticalScale(24),
  },
  formContainer: {
    width: '100%',
    flex: 1,
    justifyContent: 'space-between',
  },
  requirementRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: verticalScale(4),
  },
  requirementDot: {
    width: moderateScale(10),
    height: moderateScale(10),
    borderRadius: moderateScale(5),
    backgroundColor: theme.colors.neutralGrey,
    marginRight: moderateScale(8),
  },
  requirementText: {
    color: theme.colors.neutralGrey,
    fontSize: moderateScale(14),
  },
  requirementsContainer: {
    marginBottom: verticalScale(16),
  },
}));

export default ResetPasswordScreen;
