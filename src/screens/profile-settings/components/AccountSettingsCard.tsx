import React from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/Feather';
import { makeStyles, useTheme } from '@rneui/themed';

import { LanguageIcon, PasswordIcon, WalletIcon } from '@/constants/svgs';

interface SettingItemProps {
  icon?: React.ReactNode;
  title: string;
  subtitle?: string;
  onPress: () => void;
  isLast?: boolean;
}

const SettingItem: React.FC<SettingItemProps> = ({
  icon,
  title,
  subtitle,
  onPress,
  isLast = false,
}) => {
  const { theme } = useTheme();
  const styles = useStyles(theme);

  return (
    <TouchableOpacity style={styles.settingItem} onPress={onPress}>
      <View style={styles.settingContent}>
        <View style={styles.iconContainer}>{icon}</View>
        <View style={styles.textContainer}>
          <Text style={styles.settingTitle}>{title}</Text>
          {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
        </View>
        <Icon
          name="chevron-right"
          size={moderateScale(16)}
          color={theme.colors.grey0}
        />
      </View>
      {!isLast && <View style={styles.divider} />}
    </TouchableOpacity>
  );
};

interface AccountSettingsCardProps {
  onPayoutMethodsPress: () => void;
  onPasswordSecurityPress: () => void;
  onLanguageRegionPress: () => void;
  onNotificationsPress: () => void;
}

const AccountSettingsCard: React.FC<AccountSettingsCardProps> = ({
  onPayoutMethodsPress,
  onPasswordSecurityPress,
  onLanguageRegionPress,
  onNotificationsPress,
}) => {
  const { theme } = useTheme();
  const styles = useStyles(theme);

  return (
    <View style={styles.container}>
      <Text style={styles.sectionTitle}>Account Settings</Text>
      <View style={styles.card}>
        <SettingItem
          icon={<WalletIcon />}
          title="Payout Methods"
          onPress={onPayoutMethodsPress}
        />
        <SettingItem
          icon={<PasswordIcon />}
          title="Password & Security"
          onPress={onPasswordSecurityPress}
        />
        <SettingItem
          icon={<LanguageIcon />}
          title="Language & Region"
          subtitle="English (US) United States"
          onPress={onLanguageRegionPress}
        />
        <SettingItem
          icon={
            <Icon
              name="bell"
              size={moderateScale(20)}
              color={theme.colors.grey0}
            />
          }
          title="Notifications Preferences"
          onPress={onNotificationsPress}
          isLast
        />
      </View>
    </View>
  );
};

const useStyles = makeStyles(theme => ({
  container: {
    marginTop: verticalScale(20),
  },
  sectionTitle: {
    fontSize: moderateScale(16),
    fontWeight: '500',
    color: theme.colors.black,
    marginBottom: verticalScale(12),
  },
  card: {
    backgroundColor: theme.colors.white,
    borderRadius: moderateScale(12),
    padding: moderateScale(16),
    shadowColor: theme.colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  settingItem: {
    paddingVertical: verticalScale(12),
  },
  settingContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: moderateScale(4),
  },
  iconContainer: {
    width: moderateScale(24),
    alignItems: 'center',
    marginRight: moderateScale(12),
  },
  textContainer: {
    flex: 1,
  },
  settingTitle: {
    fontSize: moderateScale(14),
    fontWeight: '500',
    color: theme.colors.black,
    marginBottom: verticalScale(2),
  },
  settingSubtitle: {
    fontSize: moderateScale(14),
    color: theme.colors.grey2,
  },
  divider: {
    height: 1,
    backgroundColor: theme.colors.grey4,
    marginTop: verticalScale(12),
  },
}));

export default AccountSettingsCard;
