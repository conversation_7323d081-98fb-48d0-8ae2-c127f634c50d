import React from 'react';
import { Image, Pressable, Text, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/Feather';
import { useSelector } from 'react-redux';
import { makeStyles, useTheme } from '@rneui/themed';

import { CrownIcon } from '@/constants/svgs';
import { RootState } from '@/store';
import { UserProfile } from '@/types/home';

const getUserInitials = (user: UserProfile | null) => {
  if (user?.first_name && user?.last_name) {
    return `${user.first_name.charAt(0)}${user.last_name.charAt(0)}`.toUpperCase();
  } else if (user?.first_name) {
    return user.first_name.charAt(0).toUpperCase();
  } else if (user?.last_name) {
    return user.last_name.charAt(0).toUpperCase();
  }

  return null;
};

const getUserFullName = (user: UserProfile | null) => {
  if (user?.first_name && user?.last_name) {
    return `${user.first_name} ${user.last_name}`;
  } else if (user?.first_name) {
    return user.first_name;
  } else if (user?.last_name) {
    return user.last_name;
  }

  return 'User Name';
};

interface UserProfileCardProps {
  onPress?: () => void;
}

const UserProfileCard: React.FC<UserProfileCardProps> = ({ onPress }) => {
  const { theme } = useTheme();
  const styles = useStyles(theme);
  const { user } = useSelector((state: RootState) => state.home);

  return (
    <Pressable style={styles.card} onPress={onPress}>
      <View style={styles.avatarContainer}>
        {user?.avatar_url ? (
          <Image source={{ uri: user.avatar_url }} style={styles.avatar} />
        ) : (
          <View style={styles.avatarPlaceholder}>
            <Text style={styles.avatarText}>
              {getUserInitials(user) || 'U'}
            </Text>
          </View>
        )}
        <View style={styles.crownIcon}>
          <CrownIcon />
        </View>
      </View>

      <View style={styles.userInfo}>
        <Text style={styles.userName}>{getUserFullName(user)}</Text>
        <Text style={styles.userEmail}>
          {user?.email || '<EMAIL>'}
        </Text>
      </View>

      <View style={styles.chevronContainer}>
        <Icon
          name="chevron-right"
          size={moderateScale(20)}
          color={theme.colors.black}
        />
      </View>
    </Pressable>
  );
};

const useStyles = makeStyles(theme => ({
  card: {
    backgroundColor: theme.colors.white,
    borderRadius: moderateScale(12),
    borderWidth: moderateScale(1),
    borderColor: theme.colors.grey3,
    padding: moderateScale(16),
    marginTop: verticalScale(-20),
    flexDirection: 'row',
    alignItems: 'center',
    boxShadow: `0px 1px 2px 0px ${theme.colors.grey3}`,
    marginHorizontal: moderateScale(20),
  },
  avatarContainer: {
    position: 'relative',
  },
  avatar: {
    width: moderateScale(60),
    height: moderateScale(60),
    borderRadius: moderateScale(30),
  },
  avatarPlaceholder: {
    width: moderateScale(60),
    height: moderateScale(60),
    borderRadius: moderateScale(30),
    backgroundColor: theme.colors.warning,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: moderateScale(24),
    fontWeight: 'bold',
    color: theme.colors.white,
  },
  crownIcon: {
    position: 'absolute',
    bottom: -moderateScale(3),
    right: -moderateScale(3),
    width: moderateScale(30),
    height: moderateScale(30),
    borderRadius: moderateScale(15),
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  userInfo: {
    flex: 1,
    marginLeft: moderateScale(15),
  },
  userName: {
    fontSize: moderateScale(18),
    fontWeight: 'bold',
    color: theme.colors.black,
    marginBottom: verticalScale(4),
  },
  userEmail: {
    fontSize: moderateScale(14),
    color: theme.colors.grey2,
  },
  chevronContainer: {
    marginLeft: moderateScale(8),
  },
}));

export default UserProfileCard;
