import React from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/Feather';
import { makeStyles, useTheme } from '@rneui/themed';

interface SettingItemProps {
  icon?: React.ReactNode;
  title: string;
  onPress: () => void;
  isLast?: boolean;
  isRed?: boolean;
}

const SettingItem: React.FC<SettingItemProps> = ({
  icon,
  title,
  onPress,
  isLast = false,
  isRed = false,
}) => {
  const { theme } = useTheme();
  const styles = useStyles(theme);

  return (
    <TouchableOpacity style={styles.settingItem} onPress={onPress}>
      <View style={styles.settingContent}>
        <View style={styles.iconContainer}>{icon}</View>
        <View style={styles.textContainer}>
          <Text style={[styles.settingTitle, isRed && styles.redText]}>
            {title}
          </Text>
        </View>
        <Icon
          name="chevron-right"
          size={moderateScale(16)}
          color={theme.colors.grey0}
        />
      </View>
      {!isLast && <View style={styles.divider} />}
    </TouchableOpacity>
  );
};

interface OtherCardProps {
  onFaqsPress: () => void;
  onHelpCenterPress: () => void;
  onContactUsPress: () => void;
  onSignOutPress: () => void;
}

const OtherCard: React.FC<OtherCardProps> = ({
  onFaqsPress,
  onHelpCenterPress,
  onContactUsPress,
  onSignOutPress,
}) => {
  const { theme } = useTheme();
  const styles = useStyles(theme);

  return (
    <View style={styles.container}>
      <Text style={styles.sectionTitle}>Other</Text>
      <View style={styles.card}>
        <SettingItem
          icon={
            <Icon
              name="message-circle"
              size={moderateScale(20)}
              color={theme.colors.grey1}
            />
          }
          title="FAQs"
          onPress={onFaqsPress}
        />
        <SettingItem
          icon={
            <Icon
              name="help-circle"
              size={moderateScale(20)}
              color={theme.colors.grey1}
            />
          }
          title="Help Center"
          onPress={onHelpCenterPress}
        />
        <SettingItem
          icon={
            <Icon
              name="phone"
              size={moderateScale(20)}
              color={theme.colors.grey1}
            />
          }
          title="Contact Us"
          onPress={onContactUsPress}
        />
        <SettingItem
          icon={
            <Icon
              name="log-out"
              size={moderateScale(20)}
              color={theme.colors.error}
            />
          }
          title="Sign out"
          onPress={onSignOutPress}
          isLast
          isRed
        />
      </View>
    </View>
  );
};

const useStyles = makeStyles(theme => ({
  container: {
    marginTop: verticalScale(20),
  },
  sectionTitle: {
    fontSize: moderateScale(16),
    fontWeight: '500',
    color: theme.colors.black,
    marginBottom: verticalScale(12),
  },
  card: {
    backgroundColor: theme.colors.white,
    borderRadius: moderateScale(12),
    padding: moderateScale(16),
    shadowColor: theme.colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  settingItem: {
    paddingVertical: verticalScale(12),
  },
  settingContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: moderateScale(4),
  },
  iconContainer: {
    width: moderateScale(24),
    alignItems: 'center',
    marginRight: moderateScale(12),
  },
  textContainer: {
    flex: 1,
  },
  settingTitle: {
    fontSize: moderateScale(14),
    fontWeight: '500',
    color: theme.colors.black,
  },
  redText: {
    color: theme.colors.error,
  },
  divider: {
    height: 1,
    backgroundColor: theme.colors.grey4,
    marginTop: verticalScale(12),
  },
}));

export default OtherCard;
