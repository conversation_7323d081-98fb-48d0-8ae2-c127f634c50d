import React from 'react';
import { ScrollView, Text, TouchableOpacity, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { SafeAreaView } from 'react-native-safe-area-context';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/Feather';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { makeStyles, useTheme } from '@rneui/themed';

import { APP_ROUTES, AppStackNavigatorParamList } from '@/types/routes';

import { AccountSettingsCard, OtherCard, UserProfileCard } from './components';

const ProfileSettings = () => {
  const { theme } = useTheme();
  const styles = useStyles(theme);
  const navigation =
    useNavigation<NativeStackNavigationProp<AppStackNavigatorParamList>>();

  const handlePayoutMethodsPress = () => {
    navigation.navigate(APP_ROUTES.PAYOUT_METHODS);
  };

  const handlePasswordSecurityPress = () => {
    navigation.navigate(APP_ROUTES.PASSWORD_SECURITY);
  };

  const handleLanguageRegionPress = () => {
    navigation.navigate(APP_ROUTES.LANGUAGE_REGION);
  };

  const handleNotificationsPress = () => {};

  const handleFaqsPress = () => {};

  const handleHelpCenterPress = () => {};

  const handleContactUsPress = () => {};

  const handleSignOutPress = () => {};

  return (
    <SafeAreaView edges={['bottom']} style={styles.container}>
      <SafeAreaView
        edges={['top', 'left', 'right']}
        style={styles.headerSafeArea}
      >
        <LinearGradient
          colors={[theme.colors.foreground, theme.colors.brownGradient]}
        >
          <View style={styles.headerContent}>
            <View style={styles.headerRow}>
              <TouchableOpacity
                style={styles.backButton}
                onPress={() => navigation.goBack()}
              >
                <Icon
                  name="chevron-left"
                  size={moderateScale(24)}
                  color={theme.colors.white}
                />
              </TouchableOpacity>
              <Text style={styles.headerTitle}>Settings</Text>
              <View style={styles.placeholder} />
            </View>
          </View>
        </LinearGradient>
      </SafeAreaView>

      <UserProfileCard
        onPress={() => navigation.navigate(APP_ROUTES.PERSONAL_INFORMATION)}
      />
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <AccountSettingsCard
          onPayoutMethodsPress={handlePayoutMethodsPress}
          onPasswordSecurityPress={handlePasswordSecurityPress}
          onLanguageRegionPress={handleLanguageRegionPress}
          onNotificationsPress={handleNotificationsPress}
        />
        <OtherCard
          onFaqsPress={handleFaqsPress}
          onHelpCenterPress={handleHelpCenterPress}
          onContactUsPress={handleContactUsPress}
          onSignOutPress={handleSignOutPress}
        />
      </ScrollView>
    </SafeAreaView>
  );
};

export default ProfileSettings;

const useStyles = makeStyles(theme => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  headerSafeArea: {
    backgroundColor: theme.colors.foreground,
  },
  headerContent: {
    paddingBottom: verticalScale(30),
    paddingTop: verticalScale(10),
    paddingHorizontal: moderateScale(16),
    gap: verticalScale(10),
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    marginBottom: verticalScale(12),
  },
  backButton: {
    width: moderateScale(40),
    height: moderateScale(40),
    borderRadius: moderateScale(20),
    backgroundColor: `${theme.colors.white}20`,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    color: theme.colors.white,
    fontSize: moderateScale(20),
    fontWeight: '500',
    marginLeft: moderateScale(10),
  },
  placeholder: {
    width: moderateScale(40),
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: moderateScale(20),
    paddingBottom: verticalScale(20),
  },
}));
