import { NavigatorScreenParams } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

import { PromotedOffer } from './deals';

export enum AUTH_ROUTES {
  ONBOARDING = 'onboarding',
  SOCIAL_SIGNIN = 'social_signin',
  EMAIL_SIGNIN = 'email_signin',
  SIGNUP = 'signup',
  FORGOT_PASSWORD = 'forgot_password',
  RESET_PASSWORD = 'reset_password',
  CHECK_EMAIL = 'check_email',
  LANGUAGE_REGION = 'language_region',
}

export enum APP_ROUTES {
  DASHBOARD = 'Dashboard',
  HOME = 'Home',
  SETTINGS = 'Settings',
  PROFILE = 'Profile',
  PROFILE_SETTINGS = 'ProfileSettings',
  PERSONAL_INFORMATION = 'PersonalInformation',
  PAYOUT_METHODS = 'PayoutMethods',
  EDIT_PAYOUT_METHOD = 'EditPayoutMethod',
  PASSWORD_SECURITY = 'PasswordSecurity',
  CHANGE_PASSWORD = 'ChangePassword',
  DEALS = 'Deals',
  SNEAKERS = 'Sneakers',
  CASHBACK = 'Cashback',
  SINGLE_SNEAKER = 'SingleSneaker',
  DEAL_DETAIL = 'DealDetail',
  STORE_DETAIL = 'StoreDetail',
  ALL_STORES = 'AllStores',
  ALL_OFFERS = 'AllOffers',
  ALL_SNEAKERS = 'AllSneakers',
  LANGUAGE_REGION = 'LanguageRegion',
  NOTIFICATION = 'Notification',
  REWARDS = 'Rewards',
}

export enum STACKS {
  APP = 'App',
  AUTH = 'Auth',
}

export type AuthStackNavigatorParamList = {
  [AUTH_ROUTES.ONBOARDING]: undefined;
  [AUTH_ROUTES.SOCIAL_SIGNIN]: undefined;
  [AUTH_ROUTES.EMAIL_SIGNIN]: undefined;
  [AUTH_ROUTES.SIGNUP]: undefined;
  [AUTH_ROUTES.FORGOT_PASSWORD]: undefined;
  [AUTH_ROUTES.RESET_PASSWORD]: undefined;
  [AUTH_ROUTES.CHECK_EMAIL]: undefined;
  [AUTH_ROUTES.LANGUAGE_REGION]: undefined;
};

export type SneakersStackNavigatorParamList = {
  SneakersList: undefined;
  [APP_ROUTES.SINGLE_SNEAKER]: { productId: string };
};

export type BottomTabNavigatorParamList = {
  [APP_ROUTES.HOME]: undefined;
  [APP_ROUTES.SETTINGS]: undefined;
  [APP_ROUTES.PROFILE]: undefined;
  [APP_ROUTES.DEALS]: undefined;
  [APP_ROUTES.SNEAKERS]: NavigatorScreenParams<SneakersStackNavigatorParamList>;
  [APP_ROUTES.CASHBACK]: undefined;
};

export type AppStackNavigatorParamList = {
  [APP_ROUTES.DASHBOARD]: NavigatorScreenParams<BottomTabNavigatorParamList>;
  [APP_ROUTES.SINGLE_SNEAKER]: { productId: string };
  [APP_ROUTES.DEAL_DETAIL]: { deal: PromotedOffer };
  [APP_ROUTES.STORE_DETAIL]: { storeId: string };
  [APP_ROUTES.ALL_STORES]: { featured?: boolean };
  [APP_ROUTES.ALL_OFFERS]: undefined;
  [APP_ROUTES.PROFILE_SETTINGS]: undefined;
  [APP_ROUTES.PERSONAL_INFORMATION]: undefined;
  [APP_ROUTES.PAYOUT_METHODS]: undefined;
  [APP_ROUTES.EDIT_PAYOUT_METHOD]: {
    account: import('@/types/payoutMethods').BankAccount;
  };
  [APP_ROUTES.PASSWORD_SECURITY]: undefined;
  [APP_ROUTES.CHANGE_PASSWORD]: undefined;
  [APP_ROUTES.LANGUAGE_REGION]: undefined;
  [APP_ROUTES.NOTIFICATION]: undefined;
  [APP_ROUTES.REWARDS]: undefined;
};

export type RootStackParamList = {
  [STACKS.APP]: NavigatorScreenParams<AppStackNavigatorParamList>;
  [STACKS.AUTH]: NavigatorScreenParams<AuthStackNavigatorParamList>;
};

// If the stack needs to change, provide this type to useNavigation hook
export type StacksNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  keyof RootStackParamList
>;
