export interface Category {
  category_id: string;
  category_name: string;
}

export interface Region {
  region_id: string;
  name: string;
  iso_code: string;
  currency: string;
  default_language: string;
}

export interface Term {
  old_price: number;
  new_price: number;
}

export interface Store {
  store_id: string;
  store_region_id: string;
  name: string;
  region: string;
  logo: string;
}

export interface DealTerm {
  term_type: string;
  term_description: string;
  old_price: number;
  new_price: number;
  discount_percent: number;
  flat_amount: number;
  currency: string;
}

export interface VoucherTerm {
  term_id: string;
  term_title: string;
  term_description: string;
}

export interface Voucher {
  id: string;
  title: string;
  description: string;
  code: string;
  promotion_amount: number | null;
  promotion_percent: number | null;
  promotion_url: string;
  promoted: boolean;
  start_date: string;
  end_date: string;
  terms: VoucherTerm[];
  store_region_id: string;
}

export interface PromotedOffer {
  id: string;
  title: string;
  deal_url: string;
  description: string;
  store: Store;
  start_date: string;
  end_date: string;
  active: boolean;
  is_hot: boolean;
  show_on_front_page: boolean;
  promoted: boolean;
  products: unknown;
  banner_image: string;
  deal_images: string[];
  deal_type: string;
  terms: DealTerm[];
  notices: string[];
  voucher: Voucher;
  categories: string[];
  product_categories: string[];
}

export interface DealsApiResponse {
  data: PromotedOffer[];
  categories: string[];
  product_categories: string[];
  meta: {
    total: number;
    limit: number;
    offset: number;
    has_more: boolean;
  };
}
