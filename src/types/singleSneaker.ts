export interface SneakerImage {
  image_url: string;
  is_main_image: boolean;
}
export interface SneakerPrice {
  amount: string;
  currency: string;
}
export interface StoreSize {
  store_product_size_id: number;
  eu_size: string;
  us_size: string;
  uk_size: string;
  length: string;
  available: boolean;
}
export interface Launch {
  launch_type: string;
  launch_date: string;
}
export interface StoreAvailability {
  store_product_id: number;
  store_id: string;
  store_region_id: string;
  store_name: string;
  store_region: string;
  store_logo: string;
  price: string;
  currency: string;
  is_on_sale: boolean;
  sale_price: string | null;
  sale_currency: string | null;
  is_sold_out: boolean;
  available_sizes: StoreSize[];
  is_launch: boolean;
  launches: Launch[];
  product_url: string;
}
export interface SingleSneaker {
  product_id: string;
  brand: string;
  model: string;
  colorway: string;
  sku: string;
  description: string;
  release_date: string;
  promoted: boolean;
  is_hot: boolean;
  is_trending: boolean;
  is_limited: boolean;
  retail_prices: SneakerPrice[];
  images: SneakerImage[];
  categories: string[];
  colors: string[];
  launch?: Launch;
  store_availability: StoreAvailability[];
}
