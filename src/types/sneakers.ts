export interface SneakerImage {
  image_url: string;
  is_main_image: boolean;
}
export interface SneakerPrice {
  amount: string;
  currency: string;
}
export interface Sneaker {
  product_id: string;
  brand: string;
  model: string;
  colorway: string;
  sku: string;
  description: string;
  release_date: string;
  promoted: boolean;
  is_hot: boolean;
  is_trending: boolean;
  is_limited: boolean;
  retail_prices: SneakerPrice[];
  images: SneakerImage[];
  categories: string[];
  colors: string[];
  launch?: {
    launch_id: number;
    launch_title: string;
    launch_description: string;
    launch_image_url: string;
    hot_launch: boolean;
    promoted: boolean;
    limited_availability: boolean;
    launch_date: string;
  };
}
