import { HomeResponse } from './home';

export interface StoreRegion {
  id: string;
  platform_id: string;
  platform_name: string;
  store_id: string;
  vanity_id: string | null;
  vanity_url: string | null;
  currency: string;
  region: string;
  url: string;
  display_url: string;
  platform: string;
  alias: string;
  active: boolean;
  visible: boolean;
  generate: boolean;
  generator_visibility: boolean;
  visible_dashboard: boolean;
  visible_extension: boolean;
  auto_activate: boolean;
  confirmation_min_days: number;
  confirmation_max_days: number;
}

export interface Store {
  id: string;
  name: string;
  logo: string;
  description: string;
  special_deal: boolean;
  special_deal_description: string;
  special_deal_percent: number | null;
  store_regions?: StoreRegion[];
}

export interface StoreDetail {
  id: string;
  logo: string;
  name: string;
  active: boolean;
  visible: boolean;
  featured: boolean;
  featured_front_page: boolean;
  featured_landing_page: boolean;
  special_deal: boolean;
  special_deal_description: string;
  special_deal_percent: number | null;
  visible_dashboard: boolean;
  visible_extension: boolean;
  description: string;
  store_regions: StoreRegion[];
}

export interface CashbackState {
  stores: Store[];
  featuredStores: Store[];
  storeDetail: StoreDetail | null;
  homeData: HomeResponse | null;
  loading: boolean;
  featuredStoresLoading: boolean;
  error: string | null;
  featuredStoresError: string | null;
}
