// Types specific to the Home screen data

export interface UpcomingLaunch {
  launch_id: string;
  launch_title: string;
  launch_description: string;
  launch_image_url: string;
  hot_launch: boolean;
  promoted: boolean;
  limited_availability: boolean;
  product: {
    product_id: string;
    brand: string;
    model: string;
    colorway: string;
    sku: string;
    description: string;
    release_date: string; // ISO8601
    promoted: boolean;
    is_hot: boolean;
    is_trending: boolean;
    is_limited: boolean;
  };
}

export interface StoreInfo {
  store_id: string;
  store_region_id: string;
  name: string;
  region: string;
  logo: string;
}

export interface Term {
  term_type: string;
  term_description: string;
  old_price: number;
  new_price: number;
  discount_percent: number;
  flat_amount: number;
  currency: string;
}

export interface VoucherTerm {
  term_id: string;
  term_title: string;
  term_description: string;
}

export interface Voucher {
  id: string;
  title: string;
  description: string;
  code: string;
  promotion_amount: number | null;
  promotion_percent: number;
  promotion_url: string;
  promoted: boolean;
  start_date: string;
  end_date: string;
  terms: VoucherTerm[];
  store_region_id: string;
}

export interface PromotedOffer {
  id: string;
  title: string;
  deal_url: string;
  description: string;
  store: StoreInfo;
  start_date: string;
  end_date: string;
  active: boolean;
  is_hot: boolean;
  show_on_front_page: boolean;
  promoted: boolean;
  products: null;
  banner_image: string;
  deal_images: string[];
  deal_type: string;
  terms: Term[];
  notices: string[];
  voucher: Voucher;
}

export interface PromotedStore {
  id: string;
  logo: string;
  name: string;
  active: boolean;
  visible: boolean;
  featured: boolean;
  featured_front_page: boolean;
  featured_landing_page: boolean;
  special_deal: boolean;
  special_deal_description: string;
  special_deal_percent: number | null;
  visible_dashboard: boolean;
  visible_extension: boolean;
  description: string;
}

export interface HomeResponse {
  upcoming_launches: UpcomingLaunch[];
  promoted_offers: PromotedOffer[];
  promoted_stores: PromotedStore[];
}

export interface UserProfile {
  user_id: string;
  discord_id: string | null;
  email: string;
  role: string;
  avatar_url: string | null;
  referred_by: string | null;
  referral_percent: number | null;
  date_of_birth: string | null;
  first_name: string | null;
  last_name: string | null;
  discord_nickname: string | null;
  phone_number: string | null;
  has_password: boolean;
  balance: number | null;
  invite_code: string;
}

export interface BalanceSummary {
  'circle-chart': {
    pending: number;
    declined: number;
    approved: number;
    withdrawable: number;
    paid: number;
  };
  'line-chart': Record<string, number>;
}

export enum SECTION_KEY {
  HEADER = 'header',
  BANNER = 'banner',
  UPCOMING_DROPS = 'upcomingDrops',
  CASHBACK_OFFERS = 'cashbackOffers',
  POPULAR_SHOPS = 'popularShops',
}
