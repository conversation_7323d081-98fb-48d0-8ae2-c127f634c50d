import { moderateScale, scale, verticalScale } from 'react-native-size-matters';
import { createTheme } from '@rneui/themed';

declare module '@rneui/themed' {
  interface Colors {
    purpleButton: string;
    purpleButtonText: string;
    yellowBackground: string;
    yellow: string;
  }
}

export const theme = createTheme({
  lightColors: {
    base: '#F5F6F6',
    primary: '#F2B705',
    secondary: '#02261C',
    tertiary: '#C6FF29',
    background: '#ffffff',
    foreground: '#030712',
    success: '#34C759',
    error: '#FF3B30',
    warning: '#FF9F38',
    info: '#295BFF',
    neutralGrey: '#98958d',

    searchBg: '#F8F8F7',
    grey0: '#454745',
    grey1: '#999999',
    grey2: '#98958D',
    grey3: '#E8E7E6',
    border: '#F1F0F3',
    greenShadow: '#0B7C3C',
    greenDot: '#27BE69',
    cashbackBg: '#F2FAEB',
    cashbackBorder: '#A6D87E',
    cashbackText: '#4E8729',
    brownGradient: '#3a2a13',
    purpleButton: '#C4B5FD',
    purpleButtonText: '#6344C2',
    yellowBackground: '#FFF8E1',
    yellow: '#F2B705',
  },
  darkColors: {
    base: '#F8F8F7',
    primary: '#F2B705',
    secondary: '#02261C',
    tertiary: '#C6FF29',
    background: '#030712',
    foreground: '#ffffff',
    success: '#34C759',
    error: '#FF3B30',
    warning: '#FF9F38',
    info: '#295BFF',
    neutralGrey: '#98958d',
    grey0: '#454745',
    grey1: '#999999',
    grey2: '#98958D',
    border: '#F1F0F3',
    greenShadow: '#0B7C3C',
    greenDot: '#27BE69',
    cashbackBg: '#F2FAEB',
    cashbackBorder: '#A6D87E',
    cashbackText: '#4E8729',
    brownGradient: '#3a2a13',
    purpleButton: '#C4B5FD',
    purpleButtonText: '#6344C2',
    yellowBackground: '#FFF8E1',
    yellow: '#F2B705',
  },
  components: {
    Button(_, themeColors) {
      return {
        titleStyle: {
          fontFamily: 'Inter',
          color: themeColors.colors.foreground,
        },
      };
    },
    Text(_, themeColors) {
      return {
        style: {
          fontFamily: 'Inter',
          color: themeColors.colors.foreground,
        },
      };
    },
    Input(_, themeColors) {
      return {
        inputStyle: {
          fontFamily: 'Inter',
          color: themeColors.colors.foreground,
        },
        inputContainerStyle: {
          borderWidth: 1,
          borderColor: themeColors.colors.grey5,
          borderRadius: moderateScale(8),
          paddingHorizontal: scale(14),
          height: verticalScale(46),
        },
        style: {
          paddingHorizontal: 0,
        },
        containerStyle: {
          paddingHorizontal: 0,
        },
      };
    },
  },
});
