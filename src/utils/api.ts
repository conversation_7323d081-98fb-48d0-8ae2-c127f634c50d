import Toast from 'react-native-toast-message';
import axios, { AxiosError } from 'axios';

import { COMMON_ERROR } from '@/constants/common';
import { BASE_URL } from '@/constants/common/api';

import { getItemFromAS, STORAGE_KEYS } from './storage';

const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

api.interceptors.request.use(
  async config => {
    const token = await getItemFromAS(STORAGE_KEYS.AUTH_TOKEN);
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  error => Promise.reject(error),
);

api.interceptors.response.use(
  response => response,
  error => {
    return Promise.reject(error);
  },
);

export function handleApiError(error: unknown) {
  if (error instanceof AxiosError && error.response && error.response.data) {
    Toast.show({
      type: 'error',
      text1: error.response.data.detail,
    });
  } else {
    Toast.show({
      type: 'error',
      text1: COMMON_ERROR,
    });
  }
}

export default api;
