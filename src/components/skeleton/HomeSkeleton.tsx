import React from 'react';
import { View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import { makeStyles, useTheme } from '@rneui/themed';

const HomeSkeleton: React.FC = () => {
  const { theme } = useTheme();
  const styles = useStyles(theme);

  return (
    <View style={styles.loaderContainer}>
      <SkeletonPlaceholder
        borderRadius={8}
        backgroundColor={theme.colors.grey5}
      >
        {/* Header Row */}
        <View style={styles.skeletonHeaderRow}>
          <View style={styles.skeletonAvatar} />
          <View style={styles.flex1} />
          <View style={styles.skeletonAvatar} />
        </View>

        {/* Balance and Rewards Row */}
        <View style={styles.skeletonBalanceRow}>
          <View style={styles.skeletonBalanceCard} />
          <View style={styles.skeletonBalanceCard} />
        </View>

        {/* Banner Carousel */}
        <View style={styles.skeletonBannerContainer} />

        {/* Upcoming Drops Horizontal Cards */}
        <View style={styles.skeletonUpcomingDropsRow}>
          {[...Array(3)].map((_, idx) => (
            <View key={idx} style={styles.skeletonDropCard} />
          ))}
        </View>

        {/* Cashback Offers Section */}
        <View style={styles.skeletonSectionContainer}>
          <View style={styles.skeletonSectionTitle} />
          <View style={styles.skeletonCashbackRow}>
            {[...Array(3)].map((_, idx) => (
              <View key={idx} style={styles.skeletonCashbackCard} />
            ))}
          </View>
        </View>

        {/* Popular Shops Section */}
        <View style={styles.skeletonSectionContainer}>
          <View style={styles.skeletonSectionTitle} />
          <View style={styles.skeletonShopsRow}>
            {[...Array(4)].map((_, idx) => (
              <View key={idx} style={styles.skeletonShopItem} />
            ))}
          </View>
        </View>
      </SkeletonPlaceholder>
    </View>
  );
};

const useStyles = makeStyles(theme => ({
  loaderContainer: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  flex1: {
    flex: 1,
  },
  skeletonHeaderRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: verticalScale(20),
    paddingHorizontal: moderateScale(16),
    paddingTop: verticalScale(16),
  },
  skeletonAvatar: {
    width: moderateScale(40),
    height: moderateScale(40),
    borderRadius: moderateScale(20),
    marginRight: moderateScale(12),
  },
  skeletonBalanceRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: verticalScale(24),
    paddingHorizontal: moderateScale(16),
  },
  skeletonBalanceCard: {
    flex: 1,
    height: verticalScale(60),
    borderRadius: moderateScale(12),
    marginRight: moderateScale(12),
  },
  skeletonBannerContainer: {
    width: moderateScale(327),
    height: verticalScale(156),
    borderRadius: moderateScale(12),
    alignSelf: 'center',
    marginBottom: verticalScale(24),
  },
  skeletonUpcomingDropsRow: {
    flexDirection: 'row',
    paddingHorizontal: moderateScale(16),
    marginBottom: verticalScale(24),
  },
  skeletonDropCard: {
    width: moderateScale(120),
    height: verticalScale(180),
    borderRadius: moderateScale(12),
    marginRight: moderateScale(12),
  },
  skeletonSectionContainer: {
    paddingHorizontal: moderateScale(16),
    marginBottom: verticalScale(24),
  },
  skeletonSectionTitle: {
    width: moderateScale(150),
    height: verticalScale(20),
    borderRadius: moderateScale(4),
    marginBottom: verticalScale(16),
  },
  skeletonCashbackRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  skeletonCashbackCard: {
    width: moderateScale(30),
    height: verticalScale(80),
    borderRadius: moderateScale(8),
  },
  skeletonShopsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: verticalScale(24),
  },
  skeletonShopItem: {
    alignItems: 'center',
    width: moderateScale(79),
  },
}));

export default HomeSkeleton;
