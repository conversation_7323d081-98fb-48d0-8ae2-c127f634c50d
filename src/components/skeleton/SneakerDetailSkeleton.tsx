import React from 'react';
import { Platform, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import { makeStyles, useTheme } from '@rneui/themed';

const SneakerDetailSkeleton: React.FC = () => {
  const { theme } = useTheme();
  const styles = useStyles(theme);

  return (
    <View style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        {/* Image Carousel Skeleton */}
        <View style={styles.imageCarouselContainer}>
          <SkeletonPlaceholder
            borderRadius={8}
            backgroundColor={theme.colors.grey5}
          >
            {/* Main Image */}
            <View style={styles.mainImage} />

            {/* Header Overlay Buttons */}
            <View style={styles.headerOverlay}>
              <View style={styles.backButton} />
              <View style={styles.heartButton} />
            </View>

            {/* Dots Indicator */}
            <View style={styles.dotsContainer}>
              <View style={styles.dot} />
              <View style={styles.dot} />
              <View style={styles.dot} />
              <View style={styles.dot} />
            </View>
          </SkeletonPlaceholder>
        </View>

        {/* Thumbnails Skeleton */}
        <View style={styles.thumbnailsContainer}>
          <SkeletonPlaceholder
            borderRadius={8}
            backgroundColor={theme.colors.grey5}
          >
            <View style={styles.thumbnailsRow}>
              {[...Array(4)].map((_, index) => (
                <View key={index} style={styles.thumbnail} />
              ))}
            </View>
          </SkeletonPlaceholder>
        </View>

        {/* Sneaker Details Skeleton */}
        <View style={styles.detailsContainer}>
          <SkeletonPlaceholder
            borderRadius={8}
            backgroundColor={theme.colors.grey5}
          >
            {/* Product Info Section */}
            <View style={styles.infoSection}>
              <View style={styles.modelTitle} />
              <View style={styles.brandName} />
            </View>

            {/* Description Section */}
            <View style={styles.descSection}>
              <View style={styles.sectionTitle} />
              <View style={styles.descText1} />
              <View style={styles.descText2} />
              <View style={styles.descBullet1} />
              <View style={styles.descBullet2} />
            </View>
          </SkeletonPlaceholder>
        </View>

        {/* Store Section Skeleton */}
        <View style={styles.storeSection}>
          <SkeletonPlaceholder
            borderRadius={8}
            backgroundColor={theme.colors.grey5}
          >
            <View style={styles.storeSectionTitle} />

            {/* Store Cards */}
            {[...Array(3)].map((_, index) => (
              <View key={index} style={styles.storeCard}>
                {/* Store Header */}
                <View style={styles.storeHeader}>
                  <View style={styles.storeLogo} />
                  <View style={styles.storeName} />
                  <View style={styles.externalButton} />
                </View>

                {/* Cashback Text */}
                <View style={styles.cashbackText} />

                {/* Price Row */}
                <View style={styles.priceRow}>
                  <View style={styles.price} />
                  <View style={styles.oldPrice} />
                  <View style={styles.dropType} />
                </View>

                {/* Sizes Label */}
                <View style={styles.sizesLabel} />

                {/* Sizes List */}
                <View style={styles.sizesList}>
                  {[...Array(6)].map((_, sizeIndex) => (
                    <View key={sizeIndex} style={styles.sizeBox} />
                  ))}
                </View>
              </View>
            ))}
          </SkeletonPlaceholder>
        </View>
      </SafeAreaView>
    </View>
  );
};

const useStyles = makeStyles(theme => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.base,
  },
  safeArea: {
    flex: 1,
  },
  imageCarouselContainer: {
    position: 'relative',
    height: verticalScale(300),
  },
  mainImage: {
    width: '100%',
    height: '100%',
    borderRadius: 0,
  },
  headerOverlay: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? verticalScale(50) : verticalScale(20),
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: moderateScale(16),
  },
  backButton: {
    width: moderateScale(40),
    height: moderateScale(40),
    borderRadius: moderateScale(20),
    backgroundColor: theme.colors.white,
  },
  heartButton: {
    width: moderateScale(40),
    height: moderateScale(40),
    borderRadius: moderateScale(20),
    backgroundColor: theme.colors.white,
  },
  dotsContainer: {
    position: 'absolute',
    bottom: verticalScale(20),
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    gap: moderateScale(8),
  },
  dot: {
    width: moderateScale(8),
    height: moderateScale(8),
    borderRadius: moderateScale(4),
  },
  thumbnailsContainer: {
    paddingHorizontal: moderateScale(16),
    paddingVertical: verticalScale(12),
  },
  thumbnailsRow: {
    flexDirection: 'row',
    gap: moderateScale(8),
  },
  thumbnail: {
    width: moderateScale(60),
    height: moderateScale(60),
    borderRadius: moderateScale(8),
  },
  detailsContainer: {
    paddingHorizontal: moderateScale(16),
    marginBottom: verticalScale(16),
  },
  infoSection: {
    marginBottom: verticalScale(8),
  },
  modelTitle: {
    width: '80%',
    height: verticalScale(22),
    borderRadius: moderateScale(4),
    marginBottom: verticalScale(4),
  },
  brandName: {
    width: '60%',
    height: verticalScale(16),
    borderRadius: moderateScale(4),
    marginBottom: verticalScale(8),
  },
  descSection: {
    borderRadius: moderateScale(12),
    padding: moderateScale(14),
    backgroundColor: theme.colors.background,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  sectionTitle: {
    width: '70%',
    height: verticalScale(18),
    borderRadius: moderateScale(4),
    marginBottom: verticalScale(8),
  },
  descText1: {
    width: '100%',
    height: verticalScale(14),
    borderRadius: moderateScale(4),
    marginBottom: verticalScale(4),
  },
  descText2: {
    width: '90%',
    height: verticalScale(14),
    borderRadius: moderateScale(4),
    marginBottom: verticalScale(8),
  },
  descBullet1: {
    width: '60%',
    height: verticalScale(12),
    borderRadius: moderateScale(4),
    marginBottom: verticalScale(2),
  },
  descBullet2: {
    width: '50%',
    height: verticalScale(12),
    borderRadius: moderateScale(4),
    marginBottom: verticalScale(2),
  },
  storeSection: {
    paddingHorizontal: moderateScale(16),
    marginBottom: verticalScale(16),
  },
  storeSectionTitle: {
    width: '40%',
    height: verticalScale(18),
    borderRadius: moderateScale(4),
    marginBottom: verticalScale(12),
  },
  storeCard: {
    backgroundColor: theme.colors.white,
    borderRadius: moderateScale(12),
    padding: moderateScale(14),
    marginBottom: verticalScale(12),
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  storeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: verticalScale(4),
  },
  storeLogo: {
    width: moderateScale(28),
    height: moderateScale(28),
    borderRadius: moderateScale(14),
    marginRight: moderateScale(8),
  },
  storeName: {
    width: moderateScale(100),
    height: verticalScale(16),
    borderRadius: moderateScale(4),
    flex: 1,
  },
  externalButton: {
    width: moderateScale(36),
    height: moderateScale(36),
    borderRadius: moderateScale(18),
    backgroundColor: theme.colors.background,
  },
  cashbackText: {
    width: moderateScale(120),
    height: verticalScale(14),
    borderRadius: moderateScale(4),
    marginBottom: verticalScale(8),
  },
  priceRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: verticalScale(12),
  },
  price: {
    width: moderateScale(60),
    height: verticalScale(18),
    borderRadius: moderateScale(4),
    marginRight: moderateScale(8),
  },
  oldPrice: {
    width: moderateScale(50),
    height: verticalScale(16),
    borderRadius: moderateScale(4),
    marginRight: moderateScale(8),
  },
  dropType: {
    width: moderateScale(80),
    height: verticalScale(20),
    borderRadius: moderateScale(10),
  },
  sizesLabel: {
    width: moderateScale(100),
    height: verticalScale(14),
    borderRadius: moderateScale(4),
    marginBottom: verticalScale(8),
  },
  sizesList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: moderateScale(8),
  },
  sizeBox: {
    width: moderateScale(40),
    height: moderateScale(32),
    borderRadius: moderateScale(6),
  },
}));

export default SneakerDetailSkeleton;
