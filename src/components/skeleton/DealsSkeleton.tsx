import React from 'react';
import { Platform, ScrollView, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { SafeAreaView } from 'react-native-safe-area-context';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import { makeStyles, useTheme } from '@rneui/themed';

export const CategoryLoadingSkeleton: React.FC = () => {
  const { theme } = useTheme();
  const styles = useStyles(theme);

  return (
    <View style={styles.dealsContainer}>
      <SkeletonPlaceholder
        borderRadius={8}
        backgroundColor={theme.colors.grey5}
      >
        {/* Grid of deal cards */}
        {[...Array(4)].map((_, rowIdx) => (
          <View key={rowIdx} style={styles.dealsRow}>
            {[...Array(2)].map((__, colIdx) => (
              <View key={colIdx} style={styles.dealCard}>
                {/* Card Image */}
                <View style={styles.dealImageContainer}>
                  <View style={styles.dealImage} />
                </View>

                {/* Card Info */}
                <View style={styles.dealInfo}>
                  <View style={styles.dealBrand} />
                  <View style={styles.dealTitle} />

                  {/* Footer */}
                  <View style={styles.dealFooter}>
                    <View style={styles.dealDiscount} />
                    <View style={styles.dealButton} />
                  </View>
                </View>
              </View>
            ))}
          </View>
        ))}
      </SkeletonPlaceholder>
    </View>
  );
};

const DealsSkeleton: React.FC = () => {
  const { theme } = useTheme();
  const styles = useStyles(theme);

  return (
    <View style={styles.container}>
      {/* Header Skeleton */}
      <SafeAreaView
        edges={['top', 'left', 'right']}
        style={styles.headerSafeArea}
      >
        <LinearGradient
          colors={[theme.colors.foreground, theme.colors.brownGradient]}
        >
          <View style={styles.headerContent}>
            {/* Header Row */}
            <View style={styles.headerRow}>
              <SkeletonPlaceholder
                borderRadius={4}
                backgroundColor={`${theme.colors.white}20`}
              >
                <View style={styles.headerTitle} />
              </SkeletonPlaceholder>
              <SkeletonPlaceholder
                borderRadius={moderateScale(100)}
                backgroundColor={`${theme.colors.white}20`}
              >
                <View style={styles.settingsBtn} />
              </SkeletonPlaceholder>
            </View>

            {/* Category Pills Row  */}
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.pillsScroll}
            >
              {[
                'All',
                'Electronics',
                'Fashion',
                'Sports',
                'Home',
                'Beauty',
              ].map((cat, idx) => (
                <View key={idx} style={styles.chipContainer}>
                  <SkeletonPlaceholder
                    borderRadius={moderateScale(20)}
                    backgroundColor={`${theme.colors.white}20`}
                  >
                    <View
                      style={[
                        styles.chipButton,
                        { width: moderateScale(cat.length * 8 + 32) },
                      ]}
                    />
                  </SkeletonPlaceholder>
                </View>
              ))}
            </ScrollView>
          </View>
        </LinearGradient>
      </SafeAreaView>

      {/* Deals Grid Skeleton */}
      <View style={styles.dealsContainer}>
        <SkeletonPlaceholder
          borderRadius={moderateScale(8)}
          backgroundColor={theme.colors.grey5}
        >
          {/* Grid of deal cards */}
          {[...Array(4)].map((_, rowIdx) => (
            <View key={rowIdx} style={styles.dealsRow}>
              {[...Array(2)].map((__, colIdx) => (
                <View key={colIdx} style={styles.dealCard}>
                  {/* Card Image */}
                  <View style={styles.dealImageContainer}>
                    <View style={styles.dealImage} />
                  </View>

                  {/* Card Info */}
                  <View style={styles.dealInfo}>
                    <View style={styles.dealBrand} />
                    <View style={styles.dealTitle} />

                    {/* Footer */}
                    <View style={styles.dealFooter}>
                      <View style={styles.dealDiscount} />
                      <View style={styles.dealButton} />
                    </View>
                  </View>
                </View>
              ))}
            </View>
          ))}
        </SkeletonPlaceholder>
      </View>
    </View>
  );
};

const useStyles = makeStyles(theme => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  headerSafeArea: {
    backgroundColor: theme.colors.foreground,
  },
  headerContent: {
    paddingBottom: verticalScale(50),
    paddingTop: Platform.OS === 'ios' ? verticalScale(5) : verticalScale(10),
    paddingHorizontal: moderateScale(16),
    gap: verticalScale(10),
    minHeight: verticalScale(120),
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: verticalScale(12),
  },
  headerTitle: {
    width: moderateScale(80),
    height: verticalScale(24),
    borderRadius: moderateScale(4),
  },
  settingsBtn: {
    width: moderateScale(40),
    height: verticalScale(40),
    borderRadius: moderateScale(20),
  },
  pillsScroll: {
    flexDirection: 'row',
  },
  chipContainer: {
    marginRight: moderateScale(12),
  },
  chipButton: {
    height: verticalScale(40),
    borderRadius: moderateScale(20),
    paddingVertical: verticalScale(6),
    paddingHorizontal: moderateScale(16),
  },
  dealsContainer: {
    flex: 1,
    paddingHorizontal: moderateScale(8),
    paddingTop: verticalScale(12),
    paddingBottom: verticalScale(20),
  },
  dealsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: verticalScale(16),
  },
  dealCard: {
    flex: 1,
    marginHorizontal: moderateScale(4),
    borderRadius: moderateScale(8),
    backgroundColor: theme.colors.white,
    elevation: 2,
    shadowColor: theme.colors.black,
    shadowOffset: { width: 0, height: verticalScale(2) },
    shadowOpacity: 0.1,
    shadowRadius: moderateScale(4),
    overflow: 'hidden',
  },
  dealImageContainer: {
    width: '90%',
    height: verticalScale(120),
    borderWidth: moderateScale(1),
    borderColor: theme.colors.border,
    borderRadius: moderateScale(6),
    marginTop: verticalScale(8),
    alignSelf: 'center',
    alignItems: 'center',
    justifyContent: 'center',
  },
  dealImage: {
    width: '95%',
    height: '100%',
    borderRadius: moderateScale(6),
  },
  dealInfo: {
    padding: moderateScale(12),
  },
  dealBrand: {
    width: moderateScale(60),
    height: verticalScale(10),
    borderRadius: moderateScale(4),
    marginBottom: verticalScale(4),
  },
  dealTitle: {
    width: '100%',
    height: verticalScale(14),
    borderRadius: moderateScale(4),
    marginBottom: verticalScale(8),
  },
  dealFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dealDiscount: {
    width: moderateScale(80),
    height: verticalScale(14),
    borderRadius: moderateScale(4),
  },
  dealButton: {
    width: moderateScale(32),
    height: verticalScale(32),
    borderRadius: moderateScale(16),
  },
}));

export default DealsSkeleton;
