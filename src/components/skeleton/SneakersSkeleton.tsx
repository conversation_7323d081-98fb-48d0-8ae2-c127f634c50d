import React from 'react';
import { Platform, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { SafeAreaView } from 'react-native-safe-area-context';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import { makeStyles, useTheme } from '@rneui/themed';

const SneakersSkeleton: React.FC = () => {
  const { theme } = useTheme();
  const styles = useStyles(theme);

  return (
    <View style={styles.container}>
      {/* Header Skeleton */}
      <SafeAreaView
        edges={['top', 'left', 'right']}
        style={styles.headerContainer}
      >
        <LinearGradient
          colors={[theme.colors.foreground, theme.colors.brownGradient]}
        >
          <View style={styles.headerContent}>
            <SkeletonPlaceholder
              borderRadius={8}
              backgroundColor={`${theme.colors.white}20`}
            >
              {/* Header Row */}
              <View style={styles.headerRow}>
                <View style={styles.headerTitle} />
                <View style={styles.filterButton} />
              </View>
              {/* Search Box */}
              <View style={styles.searchBox} />
            </SkeletonPlaceholder>
          </View>
        </LinearGradient>
      </SafeAreaView>

      {/* Sneakers Grid Skeleton */}
      <View style={styles.sneakersContainer}>
        <SkeletonPlaceholder
          borderRadius={moderateScale(8)}
          backgroundColor={theme.colors.grey5}
        >
          {/* Grid of sneaker cards */}
          {[...Array(4)].map((_, rowIdx) => (
            <View key={rowIdx} style={styles.sneakersRow}>
              {[...Array(2)].map((__, colIdx) => (
                <View key={colIdx} style={styles.sneakerCard}>
                  {/* Card Image */}
                  <View style={styles.sneakerImageContainer}>
                    <View style={styles.sneakerImage} />
                  </View>

                  {/* Card Info */}
                  <View style={styles.sneakerInfo}>
                    <View style={styles.sneakerBrand} />
                    <View style={styles.sneakerTitle} />
                    <View style={styles.sneakerTitle2} />

                    {/* Footer */}
                    <View style={styles.sneakerFooter}>
                      <View style={styles.sneakerPrice} />
                      <View style={styles.sneakerButton} />
                    </View>
                  </View>
                </View>
              ))}
            </View>
          ))}
        </SkeletonPlaceholder>
      </View>
    </View>
  );
};

const useStyles = makeStyles(theme => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  headerContainer: {
    backgroundColor: theme.colors.foreground,
  },
  headerContent: {
    paddingBottom: verticalScale(50),
    paddingTop: Platform.OS === 'ios' ? verticalScale(5) : verticalScale(10),
    paddingHorizontal: moderateScale(16),
    gap: verticalScale(10),
    minHeight: verticalScale(120),
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: verticalScale(12),
  },
  headerTitle: {
    width: moderateScale(100),
    height: verticalScale(24),
    borderRadius: moderateScale(4),
  },
  filterButton: {
    width: moderateScale(40),
    height: verticalScale(40),
    borderRadius: moderateScale(20),
  },
  searchBox: {
    width: '100%',
    height: verticalScale(40),
    borderRadius: moderateScale(20),
    backgroundColor: `${theme.colors.grey0}10`,
    borderWidth: moderateScale(1),
    borderColor: `${theme.colors.white}40`,
    marginTop: 0,
    marginBottom: 0,
  },
  sneakersContainer: {
    flex: 1,
    paddingHorizontal: moderateScale(12),
    paddingTop: verticalScale(12),
  },
  sneakersRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: verticalScale(16),
  },
  sneakerCard: {
    flex: 1,
    marginHorizontal: moderateScale(4),
    borderRadius: moderateScale(12),
    backgroundColor: theme.colors.white,
    elevation: 2,
    shadowColor: theme.colors.black,
    shadowOffset: { width: 0, height: verticalScale(2) },
    shadowOpacity: 0.1,
    shadowRadius: moderateScale(4),
    overflow: 'hidden',
  },
  sneakerImageContainer: {
    width: '100%',
    height: verticalScale(110),
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.white,
    marginTop: verticalScale(4),
  },
  sneakerImage: {
    width: '95%',
    height: '100%',
    borderRadius: moderateScale(8),
  },
  sneakerInfo: {
    padding: moderateScale(12),
  },
  sneakerBrand: {
    width: moderateScale(60),
    height: verticalScale(12),
    borderRadius: moderateScale(4),
    marginBottom: verticalScale(2),
  },
  sneakerTitle: {
    width: '90%',
    height: verticalScale(14),
    borderRadius: moderateScale(4),
    marginBottom: verticalScale(4),
  },
  sneakerTitle2: {
    width: '70%',
    height: verticalScale(14),
    borderRadius: moderateScale(4),
    marginBottom: verticalScale(8),
  },
  sneakerFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: verticalScale(4),
  },
  sneakerPrice: {
    width: moderateScale(80),
    height: verticalScale(16),
    borderRadius: moderateScale(4),
    marginRight: moderateScale(8),
  },
  sneakerButton: {
    width: moderateScale(30),
    height: verticalScale(30),
    borderRadius: moderateScale(15),
    marginLeft: 'auto',
  },
}));

export default SneakersSkeleton;
