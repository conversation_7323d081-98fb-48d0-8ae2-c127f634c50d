import React from 'react';
import { TextStyle, ViewStyle } from 'react-native';
import { moderateScale } from 'react-native-size-matters';
import { Button, ButtonProps, Icon, makeStyles } from '@rneui/themed';

export type AppButtonType = 'primary' | 'secondary' | 'ghost' | 'icon';
export type AppButtonSize = 'large' | 'medium' | 'small' | 'icon';

interface AppButtonProps extends Omit<ButtonProps, 'type' | 'size'> {
  type?: AppButtonType;
  size?: AppButtonSize;
  iconName?: string;
  iconType?: string;
}

export const AppButton: React.FC<AppButtonProps> = ({
  type = 'primary',
  size = 'medium',
  iconName,
  iconType = 'material',
  title,
  ...rest
}) => {
  const styles = useStyles({ type, size });

  return (
    <Button
      {...rest}
      title={title}
      buttonStyle={[styles.button, rest.buttonStyle]}
      titleStyle={[styles.title, rest.titleStyle]}
      icon={
        type === 'icon' && iconName ? (
          <Icon
            name={iconName}
            type={iconType}
            color={styles.title.color}
            size={moderateScale(24)}
          />
        ) : (
          rest.icon
        )
      }
    />
  );
};

export default AppButton;

const useStyles = makeStyles(
  (theme, props: { type: AppButtonType; size: AppButtonSize }) => {
    const { colors } = theme;

    const sizeStyles: Record<
      AppButtonSize,
      { button: ViewStyle; title: TextStyle }
    > = {
      large: {
        button: {
          paddingVertical: moderateScale(12),
          paddingHorizontal: moderateScale(24),
          width: '100%',
        },
        title: { fontSize: moderateScale(18) },
      },
      medium: {
        button: {
          paddingVertical: moderateScale(12),
          paddingHorizontal: moderateScale(24),
        },
        title: { fontSize: moderateScale(16) },
      },
      small: {
        button: {
          paddingVertical: moderateScale(8),
          paddingHorizontal: moderateScale(16),
        },
        title: { fontSize: moderateScale(14) },
      },
      icon: {
        button: {
          padding: moderateScale(2),
          width: moderateScale(36),
          height: moderateScale(36),
          aspectRatio: 1,
        },
        title: { fontSize: moderateScale(14) },
      },
    };

    let buttonStyle: ViewStyle = {};
    let titleStyle: TextStyle = {};

    switch (props.type) {
      case 'primary':
        buttonStyle = { backgroundColor: colors.primary };
        titleStyle = { color: colors.foreground };
        break;
      case 'secondary':
        buttonStyle = {
          backgroundColor: `${colors.primary}20`,
          borderColor: colors.primary,
          borderWidth: 1,
        };
        titleStyle = { color: colors.primary };
        break;
      case 'ghost':
        buttonStyle = {
          backgroundColor: 'transparent',
          borderWidth: 1,
          borderColor: colors.grey5,
        };
        titleStyle = { color: colors.foreground };
        break;
      case 'icon':
        buttonStyle = {
          backgroundColor: 'transparent',
          borderWidth: 1,
          borderColor: colors.grey5,
          paddingHorizontal: 0,
        };
        titleStyle = { color: colors.foreground };
        break;
      default:
        break;
    }

    return {
      button: {
        borderRadius: moderateScale(50),
        ...buttonStyle,
        ...sizeStyles[props.size].button,
      },
      title: {
        ...titleStyle,
        ...sizeStyles[props.size].title,
      },
    };
  },
);
