import React from 'react';
import { StyleSheet, View } from 'react-native';
import { moderateScale } from 'react-native-size-matters';
import { Button, Icon, useTheme, useThemeMode } from '@rneui/themed';

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  icon: {
    marginRight: moderateScale(8),
  },
});

const ThemeModeSwitch: React.FC = () => {
  const { mode, setMode } = useThemeMode();
  const { theme } = useTheme();
  const isDark = mode === 'dark';

  return (
    <View style={styles.container}>
      <Button
        type="outline"
        onPress={() => setMode(isDark ? 'light' : 'dark')}
        icon={
          <Icon
            name={isDark ? 'weather-sunny' : 'weather-night'}
            type="material-community"
            color={theme.colors.primary}
            size={22}
            style={styles.icon}
          />
        }
        title={isDark ? 'Switch to Light Mode' : 'Switch to Dark Mode'}
        accessibilityLabel="Toggle theme mode"
      />
    </View>
  );
};

export default ThemeModeSwitch;
