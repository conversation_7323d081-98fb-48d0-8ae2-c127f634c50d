import React from 'react';
import { View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { moderateScale, scale, verticalScale } from 'react-native-size-matters';
import { SolebackLogo } from '@assets/images/svg';
import { StackHeaderProps } from '@react-navigation/stack';
import { makeStyles, Text } from '@rneui/themed';

import AppButton from './AppButton';

const AppHeader = ({ options, back, navigation }: StackHeaderProps) => {
  const styles = useStyles();

  const handleBack = () => {
    if (back) {
      navigation.goBack();
    }
  };

  const renderCenterContent = () => {
    if (options?.title) {
      return <Text style={styles.title}>{options?.title}</Text>;
    }

    return <SolebackLogo style={styles.logo} />;
  };

  return (
    <SafeAreaView edges={['top']} style={styles.container}>
      <View style={styles.header}>
        {back ? (
          <AppButton
            type="icon"
            size="icon"
            containerStyle={styles.backButtonContainer}
            buttonStyle={styles.backButton}
            iconName="chevron-left"
            onPress={handleBack}
          />
        ) : null}
        {renderCenterContent()}
      </View>
    </SafeAreaView>
  );
};

export default AppHeader;

const useStyles = makeStyles(theme => ({
  container: {
    backgroundColor: theme.colors.background,
    paddingHorizontal: moderateScale(12),
    paddingBottom: verticalScale(10),
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.grey5,
  },
  header: {
    width: '100%',
    height: moderateScale(36),
    justifyContent: 'center',
    position: 'relative',
    marginTop: verticalScale(10),
  },
  logo: {
    width: scale(120),
    height: verticalScale(60),
    marginTop: verticalScale(26),
    marginBottom: verticalScale(20),
    alignSelf: 'center',
  },
  title: {
    color: theme.colors.foreground,
    fontSize: moderateScale(20),
    textAlign: 'center',
  },
  backButtonContainer: {
    position: 'absolute',
    left: moderateScale(6),
    top: 0,
  },
  backButton: {
    zIndex: 1000,
  },
}));
