import * as React from 'react';
import Svg, { Path, Rect, SvgProps } from 'react-native-svg';

export const ResetPasswordIcon: React.FC<SvgProps> = props => {
  return (
    <Svg width={66} height={66} viewBox="0 0 66 66" fill="none" {...props}>
      <Rect x={5} y={5} width={56} height={56} rx={28} fill="#FEF2CD" />
      <Rect
        x={5}
        y={5}
        width={56}
        height={56}
        rx={28}
        stroke="#FEF8E6"
        strokeWidth={10}
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M28.7917 39.6665C28.7917 44.0157 32.3175 47.5415 36.6667 47.5415C41.016 47.5415 44.5417 44.0157 44.5417 39.6665C44.5417 35.3173 41.016 31.7915 36.6667 31.7915C32.3175 31.7915 28.7917 35.3173 28.7917 39.6665ZM40.402 37.0242C40.5727 37.4762 40.3447 37.9812 39.8926 38.1519C39.5337 38.2875 39.1013 38.5879 38.629 39.0297C38.1668 39.4621 37.715 39.9835 37.3131 40.4975C36.9129 41.0092 36.5726 41.4998 36.3322 41.8632C36.1496 42.1337 36.0081 42.3656 35.9602 42.4478C35.7944 42.7259 35.4886 42.89 35.1652 42.874C34.8417 42.858 34.5536 42.6647 34.4161 42.3715C34.098 41.6928 33.8194 41.4379 33.6987 41.352C33.6688 41.3308 33.6466 41.3184 33.6332 41.3118C33.2052 41.2546 32.8751 40.8881 32.8751 40.4445C32.8751 39.9613 33.2668 39.5695 33.7501 39.5695C33.8229 39.5709 34.001 39.5852 34.1305 39.6318C34.3009 39.6838 34.4999 39.7744 34.7129 39.9259C34.8791 40.0441 35.049 40.1957 35.2188 40.3887C35.4253 40.0933 35.667 39.7615 35.9346 39.4194C36.3691 38.8639 36.8824 38.2672 37.4335 37.7517C37.9745 37.2456 38.6037 36.7682 39.2742 36.5149C39.7263 36.3441 40.2312 36.5721 40.402 37.0242Z"
        fill="#F2B705"
      />
      <Path
        d="M37.7533 22.4583C38.7743 22.4582 39.6168 22.4582 40.3005 22.519C41.0116 22.5822 41.6621 22.7181 42.2614 23.0574C42.9547 23.4499 43.5304 24.0145 43.9307 24.6944C44.2767 25.2821 44.4153 25.9199 44.4798 26.6172C44.5417 27.2877 44.5417 28.1139 44.5417 29.1151V29.1151V29.18V29.1803C44.5417 29.8226 44.5417 30.3871 44.5272 30.8761C44.509 31.4933 43.9839 31.9792 43.3545 31.9613C42.7251 31.9433 42.2296 31.4285 42.2479 30.8112C42.2613 30.3581 42.2614 29.825 42.2614 29.1666C42.2614 28.1011 42.2603 27.3767 42.2088 26.8191C42.1587 26.2768 42.0681 26.0031 41.9559 25.8124C41.7558 25.4725 41.4679 25.1902 41.1213 24.9939C40.9268 24.8839 40.6476 24.7951 40.0947 24.746C39.5261 24.6954 38.7874 24.6944 37.7008 24.6944H26.2993C25.2127 24.6944 24.474 24.6954 23.9054 24.746C23.3524 24.7951 23.0732 24.8839 22.8788 24.9939C22.5322 25.1902 22.2443 25.4725 22.0442 25.8124C21.9319 26.0031 21.8414 26.2768 21.7913 26.8191C21.7398 27.3767 21.7387 28.1011 21.7387 29.1666C21.7387 30.2321 21.7398 30.9565 21.7913 31.5141C21.8414 32.0563 21.9319 32.3301 22.0442 32.5208C22.2443 32.8607 22.5322 33.143 22.8788 33.3392C23.0732 33.4493 23.3524 33.5381 23.9054 33.5872C24.474 33.6377 25.2127 33.6388 26.2993 33.6388H26.8694C27.499 33.6388 28.0095 34.1394 28.0095 34.7569C28.0095 35.3743 27.499 35.8749 26.8694 35.8749H26.2468H26.2467C25.2258 35.8749 24.3833 35.875 23.6995 35.8142C22.9885 35.751 22.338 35.6151 21.7387 35.2758C21.0454 34.8832 20.4697 34.3187 20.0694 33.6388C19.7234 33.0511 19.5848 32.4133 19.5203 31.7159C19.4583 31.0455 19.4584 30.2193 19.4584 29.2181V29.2181V29.1151V29.1151C19.4584 28.1139 19.4583 27.2877 19.5203 26.6172C19.5848 25.9199 19.7234 25.2821 20.0694 24.6944C20.4697 24.0145 21.0454 23.4499 21.7387 23.0574C22.338 22.7181 22.9885 22.5822 23.6995 22.519C24.3833 22.4582 25.2258 22.4582 26.2468 22.4583H26.2468H37.7533H37.7533Z"
        fill="#F2B705"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M25.0002 29.1667C25.0002 28.5223 25.5226 28 26.1669 28H26.1774C26.8217 28 27.3441 28.5223 27.3441 29.1667C27.3441 29.811 26.8217 30.3333 26.1774 30.3333H26.1669C25.5226 30.3333 25.0002 29.811 25.0002 29.1667ZM30.8336 29.1667C30.8336 28.5223 31.3559 28 32.0002 28H32.0107C32.6551 28 33.1774 28.5223 33.1774 29.1667C33.1774 29.811 32.6551 30.3333 32.0107 30.3333H32.0002C31.3559 30.3333 30.8336 29.811 30.8336 29.1667ZM36.6669 29.1667C36.6669 28.5223 37.1892 28 37.8336 28H37.8441C38.4884 28 39.0107 28.5223 39.0107 29.1667C39.0107 29.811 38.4884 30.3333 37.8441 30.3333H37.8336C37.1892 30.3333 36.6669 29.811 36.6669 29.1667Z"
        fill="#F2B705"
      />
    </Svg>
  );
};
