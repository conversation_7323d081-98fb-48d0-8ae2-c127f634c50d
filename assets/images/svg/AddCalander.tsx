import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';

export const AddCalander: React.FC<SvgProps> = props => {
  return (
    <Svg width="12" height="12" viewBox="0 0 12 12" fill="none" {...props}>
      <Path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M3 0.5C3.27614 0.5 3.5 0.72386 3.5 1V2.5C3.5 2.77614 3.27614 3 3 3C2.72386 3 2.5 2.77614 2.5 2.5V1C2.5 0.72386 2.72386 0.5 3 0.5ZM9 0.5C9.27615 0.5 9.5 0.72386 9.5 1V2.5C9.5 2.77614 9.27615 3 9 3C8.72385 3 8.5 2.77614 8.5 2.5V1C8.5 0.72386 8.72385 0.5 9 0.5Z"
        fill="#191301"
      />
      <Path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M0.75 2.75C0.75 1.92158 1.42158 1.25 2.25 1.25H9.75C10.5785 1.25 11.25 1.92158 11.25 2.75V6.5C11.25 6.77615 11.0261 7 10.75 7C10.4739 7 10.25 6.77615 10.25 6.5V4.75H1.75V10C1.75 10.2761 1.97386 10.5 2.25 10.5H5.75C6.02615 10.5 6.25 10.7239 6.25 11C6.25 11.2761 6.02615 11.5 5.75 11.5H2.25C1.42158 11.5 0.75 10.8285 0.75 10V2.75Z"
        fill="#191301"
      />
      <Path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M8.75 7C9.02615 7 9.25 7.22385 9.25 7.5V8.75H10.5C10.7761 8.75 11 8.97385 11 9.25C11 9.52615 10.7761 9.75 10.5 9.75H9.25V11C9.25 11.2761 9.02615 11.5 8.75 11.5C8.47385 11.5 8.25 11.2761 8.25 11V9.75H7C6.72385 9.75 6.5 9.52615 6.5 9.25C6.5 8.97385 6.72385 8.75 7 8.75H8.25V7.5C8.25 7.22385 8.47385 7 8.75 7Z"
        fill="#191301"
      />
    </Svg>
  );
};
