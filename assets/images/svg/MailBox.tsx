import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';

export const MailBoxIcon: React.FC<SvgProps> = props => {
  return (
    <Svg width="18" height="18" viewBox="0 0 18 18" fill="none" {...props}>
      <Path
        d="M15.75 5.25V12.75C15.75 14.4 14.4 15.75 12.75 15.75H5.25C3.6 15.75 2.25 14.4 2.25 12.75V5.25C2.25 3.6 3.6 2.25 5.25 2.25H12.75C14.4 2.25 15.75 3.6 15.75 5.25Z"
        stroke="#030712"
        stroke-width="1.5"
        stroke-miterlimit="10"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <Path
        d="M15.75 9.75V12.75C15.75 14.4 14.4 15.75 12.75 15.75H5.25C3.6 15.75 2.25 14.4 2.25 12.75V9.75H6.75C6.75 11.025 7.725 12 9 12C10.275 12 11.25 11.025 11.25 9.75H15.75Z"
        stroke="#030712"
        stroke-width="1.5"
        stroke-miterlimit="10"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <Path
        d="M6 6H12"
        stroke="#030712"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </Svg>
  );
};
