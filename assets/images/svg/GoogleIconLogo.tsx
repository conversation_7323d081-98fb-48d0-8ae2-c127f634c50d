import * as React from 'react';
import Svg, { <PERSON><PERSON><PERSON><PERSON>, Defs, G, Path, Rect, SvgProps } from 'react-native-svg';

export const GoogleIconLogo: React.FC<SvgProps> = props => {
  return (
    <Svg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
      <G clipPath="url(#clip0_13185_9258)">
        <Path
          d="M23.766 12.265C23.766 11.4832 23.6999 10.6973 23.5588 9.92822H12.24V14.3567H18.7217C18.4528 15.7849 17.5885 17.0484 16.323 17.8512V20.7247H20.19C22.4608 18.7217 23.766 15.7638 23.766 12.265Z"
          fill="#4285F4"
        />
        <Path
          d="M12.24 23.5007C15.4764 23.5007 18.2058 22.4823 20.1944 20.7245L16.3274 17.851C15.2516 18.5525 13.8626 18.9497 12.2444 18.9497C9.11376 18.9497 6.45934 16.9256 5.50693 14.2043H1.51648V17.1665C3.55359 21.0498 7.70278 23.5007 12.24 23.5007V23.5007Z"
          fill="#34A853"
        />
        <Path
          d="M5.50253 14.2043C4.99987 12.7761 4.99987 11.2295 5.50253 9.80126V6.83911H1.51649C-0.18551 10.0886 -0.18551 13.917 1.51649 17.1665L5.50253 14.2043V14.2043Z"
          fill="#FBBC04"
        />
        <Path
          d="M12.24 5.05176C13.9508 5.0264 15.6043 5.64334 16.8433 6.77581L20.2694 3.49251C18.1 1.54028 15.2207 0.46697 12.24 0.500775C7.70277 0.500775 3.55359 2.95163 1.51648 6.83919L5.50252 9.80134C6.45052 7.07583 9.10935 5.05176 12.24 5.05176V5.05176Z"
          fill="#EA4335"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_13185_9258">
          <Rect
            width={24}
            height={23}
            fill="white"
            transform="translate(0 0.5)"
          />
        </ClipPath>
      </Defs>
    </Svg>
  );
};
