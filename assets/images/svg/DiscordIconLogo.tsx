import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';

export const DiscordIconLogo: React.FC<SvgProps> = props => {
  return (
    <Svg width={22} height={18} viewBox="0 0 22 18" fill="none" {...props}>
      <Path
        d="M18.6239 2.00556C17.1976 1.35085 15.6924 0.883972 14.1459 0.616631C14.1319 0.61401 14.1173 0.615886 14.1044 0.621992C14.0915 0.628098 14.0808 0.638122 14.0739 0.650639C13.8805 0.994664 13.6663 1.44337 13.5163 1.79601C11.8254 1.54283 10.143 1.54283 8.48677 1.79601C8.33681 1.43549 8.11479 0.994664 7.92055 0.650639C7.91336 0.638407 7.90264 0.628628 7.8898 0.622578C7.87697 0.616527 7.86261 0.614487 7.84859 0.616723C6.30197 0.883442 4.79664 1.35028 3.37049 2.00547C3.35829 2.01069 3.34801 2.01956 3.34107 2.03086C0.48895 6.2919 -0.292417 10.4482 0.0909332 14.5529C0.0920171 14.5629 0.0951069 14.5727 0.10002 14.5815C0.104932 14.5903 0.111568 14.5981 0.119533 14.6043C2.00127 15.9863 3.82406 16.8252 5.61302 17.3813C5.62694 17.3854 5.64178 17.3852 5.65558 17.3807C5.66937 17.3762 5.68145 17.3676 5.69021 17.356C6.11334 16.7781 6.49055 16.1687 6.81404 15.528C6.81849 15.5192 6.82103 15.5096 6.82149 15.4997C6.82195 15.4899 6.82033 15.4801 6.81673 15.471C6.81312 15.4618 6.80762 15.4535 6.80059 15.4466C6.79355 15.4398 6.78514 15.4345 6.77591 15.4311C6.17751 15.2041 5.6078 14.9274 5.05972 14.6131C5.04973 14.6073 5.04134 14.599 5.03528 14.5891C5.02923 14.5792 5.0257 14.568 5.02501 14.5565C5.02432 14.5449 5.02649 14.5333 5.03133 14.5228C5.03617 14.5123 5.04353 14.5031 5.05276 14.4961C5.16808 14.4096 5.28348 14.3198 5.39357 14.2289C5.40336 14.2209 5.4152 14.2157 5.42777 14.214C5.44034 14.2123 5.45313 14.2141 5.46471 14.2193C9.06519 15.8632 12.963 15.8632 16.521 14.2193C16.5326 14.2138 16.5455 14.2117 16.5582 14.2132C16.571 14.2148 16.583 14.2199 16.593 14.228C16.7031 14.3188 16.8184 14.4096 16.9347 14.4961C16.9439 14.503 16.9513 14.5121 16.9562 14.5226C16.9611 14.5331 16.9634 14.5446 16.9628 14.5562C16.9622 14.5677 16.9588 14.5789 16.9528 14.5889C16.9469 14.5988 16.9386 14.6071 16.9286 14.613C16.3804 14.9333 15.806 15.2066 15.2117 15.4301C15.2025 15.4337 15.1941 15.4391 15.1871 15.4461C15.1801 15.4531 15.1747 15.4615 15.1712 15.4707C15.1677 15.4799 15.1661 15.4898 15.1667 15.4997C15.1672 15.5095 15.1699 15.5192 15.1744 15.528C15.5048 16.1678 15.882 16.7772 16.2973 17.3551C16.3058 17.367 16.3178 17.3759 16.3317 17.3806C16.3455 17.3853 16.3605 17.3856 16.3745 17.3813C18.1721 16.8251 19.9949 15.9862 21.8766 14.6043C21.8847 14.5984 21.8915 14.5908 21.8964 14.5821C21.9014 14.5734 21.9044 14.5637 21.9053 14.5537C22.364 9.80814 21.137 5.68598 18.6525 2.03169C18.6464 2.01982 18.6363 2.01061 18.6239 2.00556ZM7.35167 12.0535C6.26771 12.0535 5.37451 11.0583 5.37451 9.8361C5.37451 8.614 6.25038 7.61877 7.35176 7.61877C8.46166 7.61877 9.34615 8.62271 9.32882 9.83619C9.32882 11.0583 8.45295 12.0535 7.35167 12.0535ZM14.6619 12.0535C13.5779 12.0535 12.6847 11.0583 12.6847 9.8361C12.6847 8.614 13.5605 7.61877 14.6619 7.61877C15.7718 7.61877 16.6563 8.62271 16.639 9.83619C16.639 11.0583 15.7718 12.0535 14.6619 12.0535Z"
        fill="#5865F2"
      />
    </Svg>
  );
};
