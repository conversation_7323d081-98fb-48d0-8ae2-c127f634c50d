{"name": "soleback", "version": "0.0.1", "author": {"name": "<PERSON>", "url": "https://riyancode.me"}, "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "prepare": "husky"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"]}, "dependencies": {"@ptomasroos/react-native-multi-slider": "^2.2.2", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/netinfo": "^11.4.1", "@react-native-google-signin/google-signin": "^14.0.1", "@react-native-masked-view/masked-view": "^0.3.2", "@react-native-picker/picker": "^2.11.1", "@react-navigation/bottom-tabs": "^7.3.12", "@react-navigation/native": "^7.1.8", "@react-navigation/native-stack": "^7.3.12", "@react-navigation/stack": "^7.3.1", "@reduxjs/toolkit": "^2.8.1", "@rneui/base": "^4.0.0-rc.8", "@rneui/themed": "^4.0.0-rc.8", "axios": "^1.9.0", "i18next": "^25.1.2", "react": "19.0.0", "react-hook-form": "^7.56.3", "react-i18next": "^15.5.1", "react-native": "0.79.2", "react-native-country-picker-modal": "^2.0.0", "react-native-element-dropdown": "^2.12.4", "react-native-gesture-handler": "^2.25.0", "react-native-linear-gradient": "^2.8.3", "react-native-localize": "^3.4.1", "react-native-raw-bottom-sheet": "^3.0.0", "react-native-reanimated": "^3.18.0", "react-native-reanimated-carousel": "^4.0.2", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.10.0", "react-native-size-matters": "^0.4.2", "react-native-skeleton-placeholder": "^5.2.4", "react-native-svg": "^15.12.0", "react-native-svg-charts": "^5.4.0", "react-native-swiper": "^1.6.0", "react-native-toast-message": "^2.3.0", "react-native-vector-icons": "^10.2.0", "react-redux": "^9.2.0", "yup": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.2", "@react-native/eslint-config": "0.79.2", "@react-native/metro-config": "0.79.2", "@react-native/typescript-config": "0.79.2", "@types/jest": "^29.5.13", "@types/react": "^19.0.0", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^19.0.0", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.19.0", "eslint-config-prettier": "^10.1.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-simple-import-sort": "^12.1.1", "husky": "^9.1.7", "jest": "^29.6.3", "lint-staged": "^15.5.2", "prettier": "^3.5.3", "react-native-clean-project": "^4.0.3", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}